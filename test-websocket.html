<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Test</h1>
    <div id="status">Disconnected</div>
    <button id="connect">Connect</button>
    <button id="send">Send Test Message</button>
    <button id="disconnect">Disconnect</button>
    <div id="messages"></div>

    <script>
        let ws = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');

        function log(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            messagesDiv.appendChild(div);
            console.log(message);
        }

        function connect() {
            const wsUrl = 'ws://localhost:8081/api/v1/ws';
            log('Connecting to: ' + wsUrl);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                statusDiv.textContent = 'Connected';
                statusDiv.style.color = 'green';
                log('WebSocket connected successfully!');
            };
            
            ws.onmessage = function(event) {
                log('Received: ' + event.data);
            };
            
            ws.onerror = function(error) {
                log('WebSocket error: ' + error);
                statusDiv.textContent = 'Error';
                statusDiv.style.color = 'red';
            };
            
            ws.onclose = function() {
                statusDiv.textContent = 'Disconnected';
                statusDiv.style.color = 'black';
                log('WebSocket connection closed');
            };
        }

        function sendTestMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testMessage = {
                    type: 'query',
                    session_id: 'test_session_' + Date.now(),
                    content: JSON.stringify({
                        query: 'Hello, this is a test query',
                        web_search: false
                    })
                };
                
                log('Sending: ' + JSON.stringify(testMessage));
                ws.send(JSON.stringify(testMessage));
            } else {
                log('WebSocket is not connected');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        document.getElementById('connect').onclick = connect;
        document.getElementById('send').onclick = sendTestMessage;
        document.getElementById('disconnect').onclick = disconnect;
    </script>
</body>
</html>
