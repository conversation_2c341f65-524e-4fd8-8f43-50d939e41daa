// Simple WebSocket test script
const WebSocket = require('ws');

const wsUrl = 'ws://localhost:8081/api/v1/ws';
console.log('Connecting to:', wsUrl);

const ws = new WebSocket(wsUrl);

ws.on('open', function open() {
  console.log('WebSocket connected successfully!');
  
  // Send a test query
  const testMessage = {
    type: 'query',
    session_id: 'test_session_' + Date.now(),
    content: JSON.stringify({
      query: 'Hello, this is a test query',
      web_search: false
    })
  };
  
  console.log('Sending test message:', testMessage);
  ws.send(JSON.stringify(testMessage));
});

ws.on('message', function message(data) {
  console.log('Received message:', data.toString());
});

ws.on('error', function error(err) {
  console.error('WebSocket error:', err);
});

ws.on('close', function close() {
  console.log('WebSocket connection closed');
});

// Close connection after 10 seconds
setTimeout(() => {
  ws.close();
}, 10000);
