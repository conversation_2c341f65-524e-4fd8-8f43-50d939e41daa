# Talos

Talos is a Go-based LLM agent framework providing enterprise-grade network analysis and troubleshooting capabilities through configurable host agents and specialist teams.

## Features

* **Team-based Agent System:** Facilitates coordination among host agents.
* **DeepSeek LLM Integration:** Leverages DeepSeek for natural language understanding.
* **Specialized Agents:** Includes agents for event collection, network analysis, knowledge retrieval, and solution execution.
* **Kubernetes-native Design:** Ensures seamless integration with existing infrastructure.

## Technologies Used

### Backend (Go)

* **Go:** The primary programming language.
* **Gin:** Web framework for building APIs.
* **Cobra:** CLI framework for building powerful command-line applications.
* **Viper:** Configuration management.
* **Swagger:** API documentation generation.
* **Kubernetes Client:** For Kubernetes integration.
* **Cloudwego Eino:** LLM framework for agent capabilities.
* **Mark3Labs MCP-Go:** MCP (Multi-Cloud Platform) client.

### Frontend (Next.js)

* **Next.js:** React framework for building user interfaces.
* **React:** JavaScript library for building user interfaces.
* **Tailwind CSS:** A utility-first CSS framework for rapid UI development.
* **Radix UI:** A collection of unstyled, accessible UI components.
* **React Markdown:** For rendering Markdown content.
* **Recharts:** Composable charting library built with React and D3.
* **Sonner:** A toast library for notifications.

## Getting Started

### Prerequisites

* Go (version 1.24.2 or higher)
* Node.js (LTS version)
* pnpm (recommended for frontend dependencies) or npm
* Docker (for building and running Docker images)
* Colima (optional, for local Kubernetes environment)

### Installation

1. **Clone the repository:**

    ```bash
    git clone https://code.devops.xiaohongshu.com/cloud-native/talos.git
    cd talos
    ```

2. **Backend Dependencies:**

    ```bash
    make deps
    ```

3. **Frontend Dependencies:**

    ```bash
    cd frontend
    pnpm install # or npm install
    cd ..
    ```

### Building the Application

#### Backend

```bash
make build
```

This will create an executable `talos` in the `bin/` directory.

#### Frontend

```bash
cd frontend
pnpm build # or npm run build
cd ..
```

### Running the Application

#### Backend

The backend can be run in different modes (e.g., `agent`, `mcp`).

To run the MCP server:

```bash
./bin/talos mcp
```

To run the agent:

```bash
./bin/talos agent
```

You can also run directly using `go run`:

```bash
go run main.go mcp
go run main.go agent
```

#### Frontend

```bash
cd frontend
pnpm dev # or npm run dev
```

This will start the frontend development server, usually accessible at `http://localhost:3000`.

### Docker

You can build and run the backend using Docker:

```bash
make docker-build
make docker-run
```

### Kubernetes (Local with Colima)

If you have Colima installed, you can manage a local Kubernetes environment:

```bash
make colima-start   # Start Colima with Kubernetes
make colima-stop    # Stop Colima
make colima-delete  # Delete Colima instance
```

### Development

#### Code Quality

```bash
make fmt    # Format Go code
make vet    # Run Go vet
make lint   # Run Go linter (requires golangci-lint)
make check  # Run all code quality checks (fmt, vet, lint)
```

#### Testing

```bash
make test       # Run all Go tests
make coverage   # Generate HTML test coverage report
```

#### API Documentation

```bash
make swagger    # Generate Swagger API documentation
```

## Project Structure

```
.
├── cmd/             # Cobra CLI commands (agent, mcp, root)
├── docs/            # Swagger API documentation
├── frontend/        # Next.js frontend application
├── internal/        # Internal Go services and API handlers
├── manifests/       # Kubernetes deployment manifests
├── pkg/             # Reusable Go packages (auth, components, config, log, mcp, squad, version)
├── testdata/        # Test data
├── .dockerignore
├── .gitignore
├── .gitlab-ci.yml
├── Dockerfile
├── go.mod
├── go.sum
├── main.go          # Main Go application entry point
├── Makefile         # Build and development scripts
└── README.md        # This file
```

## Dependency

```
github.com/getkin/kin-openapi v0.118.0
```
