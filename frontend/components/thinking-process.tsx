"use client"

import { ChevronDown, ChevronR<PERSON>, Lightbulb } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import ReactMarkdown from "react-markdown"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import { vscDarkPlus } from "react-syntax-highlighter/dist/cjs/styles/prism"

interface ThinkingProcessProps {
  content: string
  isStreaming?: boolean
}

export function ThinkingProcess({ content, isStreaming = false }: ThinkingProcessProps) {
  const [isExpanded, setIsExpanded] = useState(true) // 默认展开以显示流式内容
  const contentRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    if (isStreaming && contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight
    }
  }, [content, isStreaming])

  return (
    <div className="mb-4 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 text-left"
      >
        <div className="flex items-center">
          <Lightbulb className="h-5 w-5 text-orange-500 mr-2" />
          <span className="font-medium text-gray-700 dark:text-gray-300">
            执行过程
            {isStreaming && (
              <span className="ml-2 inline-flex items-center">
                <span className="animate-pulse text-orange-500">●</span>
                <span className="ml-1 text-xs text-gray-500">实时更新中</span>
              </span>
            )}
          </span>
        </div>
        {isExpanded ? (
          <ChevronDown className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronRight className="h-5 w-5 text-gray-500" />
        )}
      </button>
      {isExpanded && (
        <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
          <div
            ref={contentRef}
            className="p-4 text-sm text-gray-600 dark:text-gray-400 max-h-[30rem] overflow-y-auto"
          >
            <ReactMarkdown
              components={{
                h1: ({ node, ...props }) => (
                  <h1 className="text-lg font-bold mt-4 mb-2 text-gray-800 dark:text-gray-200" {...props} />
                ),
                h2: ({ node, ...props }) => (
                  <h2 className="text-base font-bold mt-3 mb-2 text-gray-800 dark:text-gray-200" {...props} />
                ),
                h3: ({ node, ...props }) => (
                  <h3 className="text-sm font-bold mt-2 mb-1 text-gray-800 dark:text-gray-200" {...props} />
                ),
                p: ({ node, ...props }) => <p className="my-2 text-gray-600 dark:text-gray-400" {...props} />,
                ul: ({ node, ...props }) => (
                  <ul className="list-disc pl-4 my-2 text-gray-600 dark:text-gray-400" {...props} />
                ),
                ol: ({ node, ...props }) => (
                  <ol className="list-decimal pl-4 my-2 text-gray-600 dark:text-gray-400" {...props} />
                ),
                li: ({ node, ...props }) => <li className="my-1" {...props} />,
                a: ({ node, ...props }) => (
                  <a className="text-orange-500 hover:text-orange-600 hover:underline" {...props} />
                ),
                blockquote: ({ node, ...props }) => (
                  <blockquote
                    className="border-l-2 border-gray-300 dark:border-gray-600 pl-3 my-2 text-gray-500 dark:text-gray-500 italic"
                    {...props}
                  />
                ),
                code({ node, inline, className, children, ...props }: any) {
                  const match = /language-(\w+)/.exec(className || "")
                  return !inline && match ? (
                    <SyntaxHighlighter
                      style={vscDarkPlus as any}
                      language={match[1]}
                      PreTag="div"
                      className="rounded-md my-2 text-xs"
                    >
                      {String(children).replace(/\n$/, "")}
                    </SyntaxHighlighter>
                  ) : (
                    <code className="bg-gray-100 dark:bg-gray-800 rounded px-1 py-0.5 text-xs font-mono" {...props}>
                      {children}
                    </code>
                  )
                },
              }}
            >
              {content}
            </ReactMarkdown>
            {isStreaming && (
              <span className="ml-1 inline-flex items-center">
                <span className="animate-dots text-orange-500 text-lg">●</span>
                <span className="animate-dots-delay-1 text-orange-500 text-lg">●</span>
                <span className="animate-dots-delay-2 text-orange-500 text-lg">●</span>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
