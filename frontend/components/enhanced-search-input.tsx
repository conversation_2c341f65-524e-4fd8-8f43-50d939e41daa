"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useWebSocketStream } from "@/hooks/use-websocket-stream"
import { Atom, Command, CornerDownLeft, Globe, Search, Square, Zap } from "lucide-react"
import type React from "react"
import { useEffect, useRef, useState } from "react"
import { QuickLinks } from "./quick-links"
import { StreamingMarkdownRenderer } from "./streaming-markdown-renderer"
import { ThinkingProcess } from "./thinking-process"

export function EnhancedSearchInput() {
  // State
  const [query, setQuery] = useState("")
  const [webSearch, setWebSearch] = useState(true)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [continueInput, setContinueInput] = useState("")

  // Reference
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // WebSocket Stream Hook
  const {
    isConnected,
    isConnecting,
    isProcessing,
    isWaitingForInput,
    checkpointMessage,
    messages,
    error,
    connect,
    sendQuery,
    continueWithInput,
    cancel,
    sessionId
  } = useWebSocketStream()

  // Derived state for compatibility
  const thinking = messages.filter(m => m.type === 'status').map(m => m.content).join('\n')
  const content = messages.filter(m => m.type === 'stream').map(m => m.content).join('')
  const isLoading = isProcessing
  const isComplete = !isProcessing && !isWaitingForInput && messages.length > 0

  // 计时器效果
  useEffect(() => {
    if (isProcessing) {
      setElapsedTime(0)
      timerRef.current = setInterval(() => {
        setElapsedTime((prev) => prev + 1)
      }, 1000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isProcessing])

  // 自动连接WebSocket
  useEffect(() => {
    if (!isConnected && !isConnecting) {
      connect()
    }
  }, [isConnected, isConnecting, connect])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
      e.preventDefault()
      handleSubmit(new Event("submit") as unknown as React.FormEvent)
    }
  }

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081"

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!query.trim()) return

    // 构建查询内容
    const queryContent = JSON.stringify({
      query: query.trim(),
      web_search: webSearch
    })

    console.log('发送WebSocket查询:', queryContent)
    console.log('当前连接状态:', { isConnected, isConnecting, isProcessing })

    try {
      // 如果没有连接，先连接然后发送查询
      if (!isConnected) {
        console.log('WebSocket未连接，正在建立连接...')
        connect()
        // 注意：sendQuery 内部会处理连接状态，如果未连接会等待连接建立
      }

      sendQuery(queryContent)
      console.log('sendQuery 调用完成')
    } catch (error) {
      console.error('发送查询失败:', error)
    }
  }

  const handleStop = () => {
    cancel()
  }

  const handleContinue = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!continueInput.trim()) return

    await continueWithInput(continueInput)
    setContinueInput("")
  }

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current
    if (!textarea) return
    textarea.style.height = "24px"
    const contentHeight = textarea.scrollHeight
    const lineHeight = 24
    const maxHeight = lineHeight * 3
    textarea.style.height = `${Math.min(Math.max(contentHeight, 24), maxHeight)}px`
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [query])

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "24px"
    }
  }, [])

  const formatElapsedTime = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}秒`
    } else {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}分${remainingSeconds}秒`
    }
  }

  return (
    <div className="w-full max-w-[800px] flex flex-col gap-6">
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex flex-col rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-950">
          {/* Agentic 模式显示和模型信息 */}
          <div className="flex items-center justify-between px-4 pt-3">
            <div className="flex items-center">
              <span className="rounded-full px-3 py-1 text-sm font-medium bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white">
                Agentic
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {/* WebSocket 连接状态指示器 */}
              <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-gray-50 dark:bg-gray-800/50">
                <div className={`h-2 w-2 rounded-full ${isConnected ? 'bg-green-500' :
                    isConnecting ? 'bg-yellow-500 animate-pulse' :
                      'bg-red-500'
                  }`} />
                <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                  {isConnected ? '已连接' : isConnecting ? '连接中' : '未连接'}
                </span>
              </div>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-gray-50 dark:bg-gray-800/50 cursor-help">
                      <Atom className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                      <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">基座模型</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-xs">
                    <div className="text-sm">
                      <p className="font-medium mb-1">大语言模型</p>
                      <p className="text-xs text-gray-600 dark:text-gray-300">• DeepSeek 系列：推理规划</p>
                      <p className="text-xs text-gray-600 dark:text-gray-300">• Qwen 系列：工具调用</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* 多行文本输入框 */}
          <div className="px-4 py-2">
            <textarea
              ref={textareaRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="有什么可以帮到您？整个分析过程可能持续 5-10 分钟，请您耐性等待。"
              className="w-full min-h-[24px] h-[24px] max-h-[72px] bg-transparent outline-none resize-none overflow-y-auto"
              rows={1}
              disabled={isProcessing}
            />
          </div>

          {/* 底部工具栏 */}
          <div className="flex items-center justify-between border-t border-gray-100 px-4 py-2 dark:border-gray-800">
            <div className="flex items-center space-x-2">
              {/* 联网搜索按钮 */}
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full opacity-60 cursor-not-allowed"
                      disabled={true}
                      onClick={() => setWebSearch(!webSearch)}
                      aria-label="联网搜索（开发中）"
                    >
                      <Globe className="h-4 w-4 text-gray-400" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p className="px-2 py-1">正在开发中</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Slogan */}
            <div className="flex-1 flex justify-center items-center">
              <div className="flex items-center text-sm font-medium text-gray-600 dark:text-gray-400">
                <Zap className="h-4 w-4 text-orange-500 mr-1" />
                <span>遇事难搞，Ignite 来爆!</span>
              </div>
            </div>

            {/* 搜索/停止按钮 */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  {isProcessing ? (
                    <Button
                      type="button"
                      onClick={handleStop}
                      className="rounded-full bg-red-500 hover:bg-red-600 px-4 py-2"
                    >
                      <Square className="h-4 w-4 text-white mr-2" />
                      <span>停止</span>
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      className="rounded-full bg-orange-500 hover:bg-orange-600 px-4 py-2"
                      disabled={!query.trim() || !isConnected}
                    >
                      <div className="flex items-center space-x-1">
                        <Search className="h-4 w-4 text-white" />
                        <div className="flex items-center ml-2">
                          <Command className="h-3.5 w-3.5 text-white opacity-80" />
                          <span className="mx-0.5 text-xs text-white opacity-80">+</span>
                          <CornerDownLeft className="h-3.5 w-3.5 text-white opacity-80" />
                        </div>
                      </div>
                    </Button>
                  )}
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{isProcessing ? "点击停止生成" : isConnected ? "按下 Command+Enter 发送" : "正在连接..."}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </form>

      {/* 快速链接区域 */}
      <div className="mt-2 mb-4">
        <QuickLinks />
      </div>

      {/* 搜索结果展示区域 */}
      {isLoading && !thinking && !content && !isWaitingForInput && (
        <div className="w-full flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            正在分析中，请稍候... <span className="font-mono">{formatElapsedTime(elapsedTime)}</span>
          </p>
        </div>
      )}

      {/* Checkpoint 输入界面 */}
      {isWaitingForInput && (
        <div className="w-full space-y-4">
          {thinking && <ThinkingProcess content={thinking} isStreaming={false} />}
          {content && (
            <StreamingMarkdownRenderer
              content={content}
              isStreaming={false}
              isComplete={false}
              streamingSpeed={20}
              enableTypewriter={true}
            />
          )}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">?</span>
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
                  需要更多信息
                </h3>
                <p className="text-blue-700 dark:text-blue-300 mb-4">
                  {checkpointMessage}
                </p>
                <form onSubmit={handleContinue} className="space-y-3">
                  <textarea
                    value={continueInput}
                    onChange={(e) => setContinueInput(e.target.value)}
                    placeholder="请提供更多详细信息..."
                    className="w-full min-h-[100px] p-3 border border-blue-200 dark:border-blue-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    rows={4}
                  />
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setContinueInput("")
                        stopStream()
                      }}
                      className="px-4 py-2"
                    >
                      取消
                    </Button>
                    <Button
                      type="submit"
                      className="bg-blue-500 hover:bg-blue-600 px-4 py-2"
                      disabled={!continueInput.trim()}
                    >
                      继续分析
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {(thinking || content || error) && !isWaitingForInput && (
        <div className="w-full space-y-4">
          {thinking && <ThinkingProcess content={thinking} isStreaming={isLoading} />}
          {content && (
            <StreamingMarkdownRenderer
              content={content}
              isStreaming={isLoading}
              isComplete={isComplete}
              streamingSpeed={20}
              enableTypewriter={true}
            />
          )}
          {error && !content && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
