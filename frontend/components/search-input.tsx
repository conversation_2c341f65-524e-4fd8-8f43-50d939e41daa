"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Atom, Command, CornerDownLeft, Globe, Search, Zap } from "lucide-react"
import type React from "react"
import { useEffect, useRef, useState } from "react"
import { MarkdownRenderer } from "./markdown-renderer"
import { QuickLinks } from "./quick-links"
import { ThinkingProcess } from "./thinking-process"

interface SearchResult {
  thinking: string
  content: string
  isComplete: boolean
}

export function SearchInput() {
  // State
  const [query, setQuery] = useState("")
  const [webSearch, setWebSearch] = useState(true)
  const [result, setResult] = useState<SearchResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [elapsedTime, setElapsedTime] = useState(0)

  // Reference
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (isLoading) {
      setElapsedTime(0)
      timerRef.current = setInterval(() => {
        setElapsedTime((prev) => prev + 1)
      }, 1000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [isLoading])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === "Enter") {
      e.preventDefault()
      handleSubmit(new Event("submit") as unknown as React.FormEvent)
    }
  }

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081"

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!query.trim()) return

    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/sse`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: query })
      })

      if (!response.ok) {
        setResult({
          thinking: "",
          content: `请求错误: HTTP status ${response.status}`,
          isComplete: true
        })
        setIsLoading(false)
        return
      }

      const reader = response.body?.getReader()
      if (!reader) {
        setResult({
          thinking: "",
          content: "无法读取响应流",
          isComplete: true
        })
        setIsLoading(false)
        return
      }

      const decoder = new TextDecoder()
      let buffer = ''

      const processStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader!.read()
            if (done) break

            buffer += decoder.decode(value, { stream: true })

            const messages = buffer.split('\n\n')
            buffer = messages.pop() || ''

            for (const message of messages) {
              if (!message.trim()) continue

              try {
                const data = JSON.parse(message.split('data: ')[1])

                if (data.type === 'thinking') {
                  setResult(prev => ({
                    thinking: (prev?.thinking || '') + data.content,
                    content: prev?.content || '',
                    isComplete: false
                  }))
                } else if (data.type === 'answer') {
                  setResult(prev => ({
                    thinking: prev?.thinking || '',
                    content: (prev?.content || '') + data.content,
                    isComplete: false
                  }))
                } else if (data.type === 'done') {
                  setResult(prev => prev ? ({
                    ...prev,
                    isComplete: true
                  }) : null)
                  setIsLoading(false)
                  return
                } else if (data.type === 'error') {
                  throw new Error(data.content)
                }
              } catch (e) {
                console.error('Error parsing message:', e)
              }
            }
          }
        } catch (error) {
          console.error('Stream error:', error)
          setResult({
            thinking: "",
            content: `发生错误: ${error instanceof Error ? error.message : '未知错误'}`,
            isComplete: true
          })
        } finally {
          setIsLoading(false)
        }
      }

      processStream()
    } catch (error) {
      console.error('Request error:', error)
      setResult({
        thinking: "",
        content: `请求错误: ${error instanceof Error ? error.message : '未知错误'}`,
        isComplete: true
      })
      setIsLoading(false)
    }
  }

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current
    if (!textarea) return
    textarea.style.height = "24px"
    const contentHeight = textarea.scrollHeight
    const lineHeight = 24
    const maxHeight = lineHeight * 3
    textarea.style.height = `${Math.min(Math.max(contentHeight, 24), maxHeight)}px`
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [query])

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "24px"
    }
  }, [])

  const formatElapsedTime = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}秒`
    } else {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}分${remainingSeconds}秒`
    }
  }

  return (
    <div className="w-full max-w-[800px] flex flex-col gap-6">
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex flex-col rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-950">
          {/* Agentic 模式显示和模型信息 */}
          <div className="flex items-center justify-between px-4 pt-3">
            <div className="flex items-center">
              <span className="rounded-full px-3 py-1 text-sm font-medium bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white">
                Agentic
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-gray-50 dark:bg-gray-800/50 cursor-help">
                      <Atom className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                      <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">基座模型</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-xs">
                    <div className="text-sm">
                      <p className="font-medium mb-1">大语言模型</p>
                      <p className="text-xs text-gray-600 dark:text-gray-300">• DeepSeek 系列：推理规划</p>
                      <p className="text-xs text-gray-600 dark:text-gray-300">• Qwen 系列：工具调用</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          {/* 多行文本输入框 */}
          <div className="px-4 py-2">
            <textarea
              ref={textareaRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="有什么可以帮到您？整个分析过程可能持续 10-30 分钟，请您耐性等待。"
              className="w-full min-h-[24px] h-[24px] max-h-[72px] bg-transparent outline-none resize-none overflow-y-auto"
              rows={1}
            />
          </div>
          {/* 底部工具栏 */}
          <div className="flex items-center justify-between border-t border-gray-100 px-4 py-2 dark:border-gray-800">
            <div className="flex items-center space-x-2">
              {/* 联网搜索按钮 */}
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full opacity-60 cursor-not-allowed"
                      disabled={true}
                      onClick={() => setWebSearch(!webSearch)}
                      aria-label="联网搜索（开发中）"
                    >
                      <Globe className="h-4 w-4 text-gray-400" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p className="px-2 py-1">正在开发中</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            {/* Slogan */}
            <div className="flex-1 flex justify-center items-center">
              <div className="flex items-center text-sm font-medium text-gray-600 dark:text-gray-400">
                <Zap className="h-4 w-4 text-orange-500 mr-1" />
                <span>遇事难搞，Ignite 来爆!</span>
              </div>
            </div>
            {/* 搜索按钮 */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="submit"
                    className="rounded-full bg-orange-500 hover:bg-orange-600 px-4 py-2"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                        <span>分析中...</span>
                      </>
                    ) : (
                      <div className="flex items-center space-x-1">
                        <Search className="h-4 w-4 text-white" />
                        <div className="flex items-center ml-2">
                          <Command className="h-3.5 w-3.5 text-white opacity-80" />
                          <span className="mx-0.5 text-xs text-white opacity-80">+</span>
                          <CornerDownLeft className="h-3.5 w-3.5 text-white opacity-80" />
                        </div>
                      </div>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>按下 Command+Enter 发送</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </form>
      {/* 快速链接区域 */}
      <div className="mt-2 mb-4">
        <QuickLinks />
      </div>
      {/* 搜索结果展示区域 */}
      {isLoading && !result && (
        <div className="w-full flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            正在分析中，请稍候... <span className="font-mono">{formatElapsedTime(elapsedTime)}</span>
          </p>
        </div>
      )}
      {result && (
        <div className="w-full space-y-4">
          {result.thinking && <ThinkingProcess content={result.thinking} isStreaming={isLoading} />}
          {result.content && (
            <MarkdownRenderer
              content={result.content}
              isStreaming={isLoading}
              isComplete={result.isComplete}
            />
          )}
        </div>
      )}
    </div>
  )
}
