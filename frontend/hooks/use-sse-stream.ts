"use client"

import { useCallback, useRef, useState } from "react"

interface SSEMessage {
  type: string
  content: string
}

interface SSEStreamState {
  thinking: string
  content: string
  isLoading: boolean
  isComplete: boolean
  error: string | null
  isWaitingForInput: boolean
  checkpointMessage: string
  sessionId: string | null
}

interface UseSSEStreamOptions {
  onMessage?: (message: SSEMessage) => void
  onError?: (error: Error) => void
  onComplete?: () => void
  onCheckpoint?: (message: string, sessionId: string) => void
}

export function useSSEStream(options: UseSSEStreamOptions = {}) {
  const [state, setState] = useState<SSEStreamState>({
    thinking: "",
    content: "",
    isLoading: false,
    isComplete: false,
    error: null,
    isWaitingForInput: false,
    checkpointMessage: "",
    sessionId: null
  })

  const abortControllerRef = useRef<AbortController | null>(null)

  const startStream = useCallback(async (url: string, requestBody: any) => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 创建新的 AbortController
    abortControllerRef.current = new AbortController()

    setState({
      thinking: "",
      content: "",
      isLoading: true,
      isComplete: false,
      error: null,
      isWaitingForInput: false,
      checkpointMessage: "",
      sessionId: requestBody.session_id || null
    })

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error("无法读取响应流")
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let lastActivityTime = Date.now()

      while (true) {
        // 检查是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          break
        }

        const { done, value } = await reader.read()
        if (done) break

        lastActivityTime = Date.now()
        buffer += decoder.decode(value, { stream: true })

        // 处理 SSE 消息 - 按照标准SSE格式解析
        const events = buffer.split('\n\n')
        buffer = events.pop() || '' // 保留最后一个事件（可能不完整）

        for (const event of events) {
          if (!event.trim()) continue

          try {
            // 解析SSE事件格式
            const lines = event.split('\n')
            let eventType = 'message'
            let data = ''

            for (const line of lines) {
              if (line.startsWith('event: ')) {
                eventType = line.slice(7).trim()
              } else if (line.startsWith('data: ')) {
                data = line.slice(6).trim()
              }
            }

            if (!data) continue

            // 解析JSON数据
            const messageData: SSEMessage = JSON.parse(data)

            // 调用回调函数
            options.onMessage?.(messageData)

            // 更新状态
            setState(prev => {
              const newState = { ...prev }

              switch (messageData.type) {
                case 'thinking':
                  newState.thinking = prev.thinking + messageData.content
                  break
                case 'answer':
                  newState.content = prev.content + messageData.content
                  break
                case 'done':
                  newState.isLoading = false
                  newState.isComplete = true
                  newState.isWaitingForInput = false
                  options.onComplete?.()
                  return newState
                case 'error':
                  console.error('SSE Error:', messageData.content)
                  newState.error = messageData.content
                  newState.isLoading = false
                  newState.isComplete = true
                  newState.isWaitingForInput = false
                  return newState
                case 'checkpoint':
                  console.log('Checkpoint reached, waiting for user input')
                  newState.isLoading = false
                  newState.isWaitingForInput = true
                  newState.checkpointMessage = messageData.content
                  // Extract session ID from the message if available
                  if ('session_id' in messageData) {
                    newState.sessionId = (messageData as any).session_id
                  }
                  options.onCheckpoint?.(messageData.content, newState.sessionId || '')
                  return newState
                case 'status':
                  // 状态消息，不需要特殊处理
                  break
                case 'connected':
                  // 连接建立消息，记录日志
                  console.log('SSE connection established')
                  break
                default:
                  console.warn('Unknown message type:', messageData.type)
              }

              return newState
            })
          } catch (e) {
            console.error('Error parsing SSE event:', e, 'Event:', event)
          }
        }
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        isComplete: true
      }))

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // 请求被取消，不需要处理
        console.log('SSE stream was aborted')
        return
      }

      console.error('SSE stream error:', error)
      const errorMessage = error instanceof Error ? error.message : '未知错误'

      // 检查是否是网络错误
      let userFriendlyMessage = errorMessage
      if (errorMessage.includes('fetch') || errorMessage.includes('network')) {
        userFriendlyMessage = '网络连接错误，请检查网络连接后重试'
      } else if (errorMessage.includes('HTTP error')) {
        userFriendlyMessage = '服务器响应错误，请稍后重试'
      }

      setState({
        thinking: "",
        content: `发生错误: ${userFriendlyMessage}`,
        isLoading: false,
        isComplete: true,
        error: errorMessage,
        isWaitingForInput: false,
        checkpointMessage: "",
        sessionId: null
      })

      options.onError?.(error instanceof Error ? error : new Error(errorMessage))
    }
  }, [options])

  const stopStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
    setState(prev => ({
      ...prev,
      isLoading: false
    }))
  }, [])

  const resetState = useCallback(() => {
    setState({
      thinking: "",
      content: "",
      isLoading: false,
      isComplete: false,
      error: null,
      isWaitingForInput: false,
      checkpointMessage: "",
      sessionId: null
    })
  }, [])

  const continueWithInput = useCallback(async (userInput: string) => {
    if (!state.sessionId) {
      console.error('No session ID available for continuing')
      return
    }

    try {
      // Send user input to continue endpoint
      const response = await fetch('/api/v1/sse/continue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: state.sessionId,
          content: userInput
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Update state to continue loading
      setState(prev => ({
        ...prev,
        isWaitingForInput: false,
        isLoading: true,
        checkpointMessage: ""
      }))

    } catch (error) {
      console.error('Error continuing stream:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '继续输入时发生错误',
        isWaitingForInput: false,
        isLoading: false
      }))
    }
  }, [state.sessionId])

  return {
    ...state,
    startStream,
    stopStream,
    resetState,
    continueWithInput
  }
}
