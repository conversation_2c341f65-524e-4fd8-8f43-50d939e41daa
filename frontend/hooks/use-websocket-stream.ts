import { useCallback, useEffect, useRef, useState } from 'react'

// Message types matching backend protocol
export type MessageType =
  | 'query'
  | 'continue'
  | 'cancel'
  | 'ping'
  | 'status'
  | 'stream'
  | 'checkpoint'
  | 'complete'
  | 'error'
  | 'pong'
  | 'user_confirmation_required'

// Client message structure
export interface ClientMessage {
  type: MessageType
  session_id: string
  content?: string
  timestamp?: string
}

// Server message structure
export interface ServerMessage {
  type: MessageType
  session_id: string
  content?: string
  error?: string
  timestamp: string
}

// WebSocket connection state
export interface WebSocketState {
  isConnected: boolean
  isConnecting: boolean
  isProcessing: boolean
  isWaitingForInput: boolean
  sessionId: string
  messages: ServerMessage[]
  checkpointMessage: string
  error: string | null
}

// WebSocket hook
export function useWebSocketStream() {
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    isProcessing: false,
    isWaitingForInput: false,
    sessionId: '',
    messages: [],
    checkpointMessage: '',
    error: null
  })

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttemptsRef = useRef(0)

  // Generate session ID
  const generateSessionId = useCallback(() => {
    return 'session_' + Math.random().toString(36).substring(2, 9) + '_' + Date.now()
  }, [])

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN || wsRef.current?.readyState === WebSocket.CONNECTING) {
      return
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }))

    try {
      // Get the API base URL from environment variable or default to localhost:8081
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081"
      const apiUrl = new URL(apiBaseUrl)
      const protocol = apiUrl.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = `${protocol}//${apiUrl.host}/api/v1/ws`

      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        console.log('WebSocket connected')
        reconnectAttemptsRef.current = 0 // Reset reconnect attempts on successful connection
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          sessionId: prev.sessionId || generateSessionId(),
          error: null
        }))

        // Start ping interval
        pingIntervalRef.current = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            sendMessage({
              type: 'ping',
              session_id: state.sessionId
            })
          }
        }, 30000) // Ping every 30 seconds
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: ServerMessage = JSON.parse(event.data)
          handleServerMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }))

        // Clear ping interval
        if (pingIntervalRef.current) {
          clearInterval(pingIntervalRef.current)
          pingIntervalRef.current = null
        }

        // Attempt to reconnect with exponential backoff, if not a clean close
        if (!event.wasClean && reconnectAttemptsRef.current < 5) { // Limit to 5 attempts
          const delay = Math.pow(2, reconnectAttemptsRef.current) * 1000 // 1s, 2s, 4s, 8s, 16s
          console.log(`Attempting to reconnect in ${delay / 1000}s...`)
          reconnectTimeoutRef.current = setTimeout(connect, delay)
          reconnectAttemptsRef.current++
        }
      }

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error)
        setState(prev => ({
          ...prev,
          error: 'WebSocket连接错误',
          isConnecting: false
        }))
      }

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setState(prev => ({
        ...prev,
        error: '无法建立WebSocket连接',
        isConnecting: false
      }))
    }
  }, [generateSessionId, state.sessionId])

  // Handle server messages
  const handleServerMessage = useCallback((message: ServerMessage) => {
    setState(prev => {
      const newState = { ...prev }

      switch (message.type) {
        case 'status':
          newState.messages = [...prev.messages, message]
          break

        case 'stream':
          newState.messages = [...prev.messages, message]
          break

        case 'checkpoint': // Planner asks for more info
        case 'user_confirmation_required': // Doer asks for tool confirmation
          newState.isWaitingForInput = true
          newState.isProcessing = false
          newState.checkpointMessage = message.content || '需要您提供更多信息或进行确认'
          newState.messages = [...prev.messages, message]
          break

        case 'complete':
          newState.isProcessing = false
          newState.isWaitingForInput = false
          newState.messages = [...prev.messages, message]
          break

        case 'error':
          newState.error = message.error || '发生未知错误'
          newState.isProcessing = false
          newState.isWaitingForInput = false
          newState.messages = [...prev.messages, message]
          break

        case 'pong':
          // Handle pong response (keep-alive)
          break

        default:
          console.warn('Unknown message type:', message.type)
      }

      return newState
    })
  }, [])

  // Send message to server
  const sendMessage = useCallback((message: ClientMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
    } else {
      console.error('WebSocket is not connected')
    }
  }, [])

  // Send query
  const sendQuery = useCallback((content: string) => {
    if (!state.isConnected) {
      connect()
      return
    }

    setState(prev => ({
      ...prev,
      isProcessing: true,
      isWaitingForInput: false,
      messages: [],
      error: null
    }))

    sendMessage({
      type: 'query',
      session_id: state.sessionId,
      content
    })
  }, [state.isConnected, state.sessionId, connect, sendMessage])

  // Continue with additional input
  const continueWithInput = useCallback((content: string) => {
    setState(prev => ({
      ...prev,
      isWaitingForInput: false,
      isProcessing: true,
      checkpointMessage: ''
    }))

    sendMessage({
      type: 'continue',
      session_id: state.sessionId,
      content
    })
  }, [state.sessionId, sendMessage])

  // Cancel current operation
  const cancel = useCallback(() => {
    sendMessage({
      type: 'cancel',
      session_id: state.sessionId
    })

    setState(prev => ({
      ...prev,
      isProcessing: false,
      isWaitingForInput: false,
      checkpointMessage: ''
    }))
  }, [state.sessionId, sendMessage])

  // Disconnect
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current)
      pingIntervalRef.current = null
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected')
      wsRef.current = null
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      isProcessing: false,
      isWaitingForInput: false
    }))
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    ...state,
    connect,
    disconnect,
    sendQuery,
    continueWithInput,
    cancel
  }
}
