package api

import (
	"context"
	"log/slog"

	_ "code.devops.xiaohongshu.com/cloud-native/talos/docs"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"code.devops.xiaohongshu.com/cloud-native/talos/internal/api/handlers"
	"code.devops.xiaohongshu.com/cloud-native/talos/internal/api/middleware"
	websocketHandler "code.devops.xiaohongshu.com/cloud-native/talos/internal/api/websocket"
	"code.devops.xiaohongshu.com/cloud-native/talos/internal/service"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad"

	"github.com/gin-gonic/gin"
)

// @title           Talos API
// @version         1.0
// @description     Talos API 文档
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    https://rke.devops.xiaohongshu.com
// @license.name   Apache 2.0
// @license.url    http://www.apache.org/licenses/LICENSE-2.0.html
// @host      localhost:8081
func SetupRouter() (*gin.Engine, error) {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}

	// Initialize router with appropriate mode
	gin.SetMode(cfg.GinMode)
	// Create a custom gin engine with production-ready middleware
	router := gin.New()
	// Add recovery middleware to recover from panics
	router.Use(gin.Recovery())
	// Add structured logging middleware
	router.Use(middleware.StructuredLogger())

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, SSE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Initialize squad
	squad, err := squad.New(context.Background(), cfg.Squad)
	if err != nil {
		return nil, err
	}

	// API v1 group
	v1 := router.Group("/api/v1")
	{
		// Register research routes
		researchService := service.NewResearchService(squad)

		// SSE routes (legacy)
		streamHandler := handlers.NewStreamHandler(researchService)
		v1.POST("/sse", streamHandler.HandleStream)
		v1.POST("/sse/continue", streamHandler.ContinueStream)

		// WebSocket routes (new)
		wsHandler := websocketHandler.NewWebSocketHandler(researchService)
		v1.GET("/ws", wsHandler.HandleWebSocket)
	}

	// Health check endpoints
	router.GET("/health", handlers.NewHealthHandler().CheckHealth)

	// Swagger
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler,
		ginSwagger.URL("/swagger/doc.json"),     // The url pointing to API definition
		ginSwagger.DefaultModelsExpandDepth(-1), // Hide models section
	))

	slog.Info("Router setup completed successfully")
	return router, nil
}
