package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"code.devops.xiaohongshu.com/cloud-native/talos/internal/service"
	taloscallbacks "code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/callbacks"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Message represents a structured message for SSE
type Message struct {
	Type      string `json:"type"`
	Content   string `json:"content"`
	SessionID string `json:"session_id,omitempty"`
}

// UserInputWaiter manages waiting for user input during interrupts
type UserInputWaiter struct {
	inputChan     chan string
	timeout       time.Duration
	checkpointKey string // Store the checkpoint key for resuming
}

// NewUserInputWaiter creates a new user input waiter
func NewUserInputWaiter(timeout time.Duration, checkpointKey string) *UserInputWaiter {
	return &UserInputWaiter{
		inputChan:     make(chan string, 1),
		timeout:       timeout,
		checkpointKey: checkpointKey,
	}
}

// WaitForInput waits for user input with timeout
func (w *UserInputWaiter) WaitForInput(ctx context.Context) (string, error) {
	select {
	case input := <-w.inputChan:
		return input, nil
	case <-time.After(w.timeout):
		return "", fmt.Errorf("timeout waiting for user input")
	case <-ctx.Done():
		return "", ctx.Err()
	}
}

// ProvideInput provides user input to the waiter
func (w *UserInputWaiter) ProvideInput(input string) {
	select {
	case w.inputChan <- input:
	default:
		// Channel is full, ignore
	}
}

// StreamHandler handles streaming requests
type StreamHandler struct {
	ResearchService *service.ResearchService
	// Map to store user input waiters by session ID
	inputWaiters sync.Map
}

// NewStreamHandler creates a new StreamHandler
func NewStreamHandler(researchService *service.ResearchService) *StreamHandler {
	return &StreamHandler{ResearchService: researchService}
}

// ContinueRequest represents a request to continue an interrupted conversation
type ContinueRequest struct {
	SessionID string `json:"session_id" binding:"required"`
	Content   string `json:"content" binding:"required"`
}

// ContinueStream handles continuing an interrupted conversation
func (h *StreamHandler) ContinueStream(c *gin.Context) {
	var req ContinueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Find the input waiter for this session
	if waiterInterface, ok := h.inputWaiters.Load(req.SessionID); ok {
		if waiter, ok := waiterInterface.(*UserInputWaiter); ok {
			// Provide the user input to the waiter
			waiter.ProvideInput(req.Content)
			c.JSON(200, gin.H{"status": "input_received"})
			return
		}
	}

	c.JSON(404, gin.H{"error": "No active session found"})
}

// HandleStream handles a streaming research request.
// @Summary 处理流式研究请求
// @Description 处理流式研究请求并返回SSE流
// @Tags research
// @Accept json
// @Produce text/event-stream
// @Param request body service.Request true "Research request"
// @Success 200 {string} string "SSE流"
// @Router /api/v1/sse [post]
func (h *StreamHandler) HandleStream(c *gin.Context) {
	// Set headers for SSE
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	c.Writer.Header().Set("Access-Control-Allow-Origin", "*")

	// Parse request body
	var req service.Request
	if err := c.ShouldBindJSON(&req); err != nil {
		msg := Message{
			Type:    "error",
			Content: fmt.Sprintf("Invalid request body: %v", err),
		}
		sendSSEMessage(c, msg)
		return
	}

	if req.Content == "" && req.SessionID == "" {
		msg := Message{
			Type:    "error",
			Content: "Content or session_id field is required in request body",
		}
		sendSSEMessage(c, msg)
		return
	}

	// Create a new session ID if it's not provided
	if req.SessionID == "" {
		req.SessionID = uuid.New().String()
	}

	// Create a channel to handle client disconnection
	clientGone := c.Writer.CloseNotify()

	// Start processing stream
	logChannel := taloscallbacks.DefaultLogHandler.GetEventChannel()

	go func() {
		for {
			select {
			case <-clientGone:
				log.Println("Client disconnected")
				return
			case event, ok := <-logChannel:
				if !ok {
					return
				}
				msg := Message{
					Type:      "thinking",
					Content:   event.String(),
					SessionID: req.SessionID,
				}
				sendSSEMessage(c, msg)
				c.Writer.Flush()
			}
		}
	}()

	// Process the streaming request with interrupt loop
	var streamReader *schema.StreamReader[*schema.Message]
	for {
		var err error
		streamReader, err = h.ResearchService.Stream(c.Request.Context(), req)
		if err != nil {
			interruptInfo, ok := compose.ExtractInterruptInfo(err)
			if ok {
				// Handle interrupt - wait for user input
				log.Printf("Interrupt detected for session %s, waiting for user input", req.SessionID)

				// Extract checkpoint key from interrupt info
				checkpointKey := ""
				// if interruptInfo != nil && interruptInfo.(*state.MemoryCheckPointStore)。 != "" {
				// 	checkpointKey = interruptInfo.CheckPointKey
				// }

				_ = interruptInfo

				// Create a user input waiter
				waiter := NewUserInputWaiter(5*time.Minute, checkpointKey) // 5 minute timeout
				h.inputWaiters.Store(req.SessionID, waiter)

				// Send checkpoint message to frontend
				msg := Message{
					Type:      "checkpoint",
					Content:   "需要更多信息来继续分析，请提供更多详细信息：",
					SessionID: req.SessionID,
				}
				sendSSEMessage(c, msg)
				c.Writer.Flush()

				// Wait for user input
				userInput, err := waiter.WaitForInput(c.Request.Context())
				if err != nil {
					log.Printf("Error waiting for user input: %v", err)
					msg := Message{
						Type:      "error",
						Content:   fmt.Sprintf("等待用户输入超时或出错: %v", err),
						SessionID: req.SessionID,
					}
					sendSSEMessage(c, msg)
					// Clean up waiter
					h.inputWaiters.Delete(req.SessionID)
					return
				}

				// Clean up waiter
				h.inputWaiters.Delete(req.SessionID)

				// Update request content with user input and continue loop
				req.Content = userInput
				log.Printf("Received user input for session %s, continuing stream", req.SessionID)

				// Send status message
				msg = Message{
					Type:      "status",
					Content:   "收到您的输入，继续分析中...",
					SessionID: req.SessionID,
				}
				sendSSEMessage(c, msg)
				c.Writer.Flush()

				continue // Restart the stream with new input
			}

			// Non-interrupt error
			msg := Message{
				Type:      "error",
				Content:   fmt.Sprintf("Failed to start stream: %v", err),
				SessionID: req.SessionID,
			}
			sendSSEMessage(c, msg)
			return
		}

		// Stream started successfully, break out of interrupt loop
		defer streamReader.Close()
		break
	}

	// Send initial message
	msg := Message{
		Type:      "status",
		Content:   "Starting handling...",
		SessionID: req.SessionID,
	}
	sendSSEMessage(c, msg)

	// Result stream
	go func() {
		for {
			select {
			case <-clientGone:
				log.Println("Client disconnected")
				return
			default:
				message, err := streamReader.Recv()
				if err != nil {
					if err.Error() == "EOF" {
						msg := Message{
							Type:      "done",
							Content:   "Handling completed",
							SessionID: req.SessionID,
						}
						sendSSEMessage(c, msg)
						return
					} else {
						msg := Message{
							Type:      "error",
							Content:   fmt.Sprintf("Stream error: %v", err),
							SessionID: req.SessionID,
						}
						sendSSEMessage(c, msg)
						return
					}
				}

				if message == nil {
					log.Println("Received nil message from stream")
					continue
				}

				// Send the answer content
				msg := Message{
					Type:      "answer",
					Content:   message.Content,
					SessionID: req.SessionID,
				}
				sendSSEMessage(c, msg)

				// Ensure the response is flushed to the client
				c.Writer.Flush()
			}
		}
	}()

	taloscallbacks.DefaultLogHandler.WaitStreamOutput()
}

// sendSSEMessage sends a JSON-encoded message as an SSE event
func sendSSEMessage(c *gin.Context, msg Message) {
	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("Error marshaling message: %v", err)
		return
	}

	// Write the event in SSE format
	_, err = fmt.Fprintf(c.Writer, "event: %s\ndata: %s\n\n", msg.Type, data)
	if err != nil {
		log.Printf("Error writing to response: %v", err)
	}
}
