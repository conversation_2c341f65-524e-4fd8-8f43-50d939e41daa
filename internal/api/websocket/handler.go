package websocket

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"sync"

	"code.devops.xiaohongshu.com/cloud-native/talos/internal/service"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/state"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// WebSocketHandler handles WebSocket connections for streaming research
type WebSocketHandler struct {
	ResearchService *service.ResearchService
	upgrader        websocket.Upgrader
	connManager     *ConnectionManager
	sessions        sync.Map // sessionID -> *SessionState
}

// SessionState represents the state of a WebSocket session
type SessionState struct {
	SessionID     string
	Conn          *websocket.Conn
	Context       context.Context
	Cancel        context.CancelFunc
	IsProcessing  bool
	CheckpointKey string
	mu            sync.RWMutex
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(researchService *service.ResearchService) *WebSocketHandler {
	return &WebSocketHandler{
		ResearchService: researchService,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow all origins for development
				// In production, implement proper origin checking
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		connManager: NewConnectionManager(),
	}
}

// HandleWebSocket handles WebSocket connections
// @Summary 处理流式研究请求
// @Description 处理流式研究请求并返回WebSocket流
// @Tags research
// @Accept json
// @Produce json
// @Param request body service.Request true "Research request"
// @Success 200 {string} string "WebSocket流"
// @Router /api/v1/ws [get]
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		slog.Error("Failed to upgrade to WebSocket", slog.Any("error", err))
		return
	}
	defer conn.Close()

	// Generate session and connection IDs
	sessionID := uuid.New().String()
	connID := uuid.New().String()

	// Add connection to manager
	h.connManager.AddConnection(sessionID, connID)
	defer h.connManager.RemoveConnection(sessionID)

	// Create session context
	ctx, cancel := context.WithCancel(c.Request.Context())
	defer cancel()

	// Create session state
	session := &SessionState{
		SessionID: sessionID,
		Conn:      conn,
		Context:   ctx,
		Cancel:    cancel,
	}
	h.sessions.Store(sessionID, session)
	defer h.sessions.Delete(sessionID)

	// Send welcome message
	welcomeMsg := NewServerMessage(ServerStatus, sessionID, "连接已建立，请发送您的查询")
	h.sendMessage(session, welcomeMsg)

	// Handle messages
	h.handleConnection(session)
}

// handleConnection handles messages for a WebSocket connection
func (h *WebSocketHandler) handleConnection(session *SessionState) {
	for {
		select {
		case <-session.Context.Done():
			return
		default:
			// Read message from client
			_, messageData, err := session.Conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					slog.Error("WebSocket error", slog.Any("error", err))
				}
				return
			}

			// Parse client message
			clientMsg, err := ParseClientMessage(messageData)
			if err != nil {
				slog.Error("Failed to parse client message", slog.Any("error", err))
				errorMsg := NewServerError(session.SessionID, "消息格式错误")
				h.sendMessage(session, errorMsg)
				continue
			}

			// Update session ID if provided
			if clientMsg.SessionID != "" {
				session.SessionID = clientMsg.SessionID
			}

			// Handle different message types
			switch clientMsg.Type {
			case ClientQuery:
				h.handleQuery(session, clientMsg)
			case ClientContinue:
				h.handleContinue(session, clientMsg)
			case ClientCancel:
				h.handleCancel(session)
			case ClientPing:
				h.handlePing(session)
			default:
				errorMsg := NewServerError(session.SessionID, "未知的消息类型")
				h.sendMessage(session, errorMsg)
			}
		}
	}
}

// handleQuery handles initial query from client
func (h *WebSocketHandler) handleQuery(session *SessionState, msg *ClientMessage) {
	session.mu.Lock()
	if session.IsProcessing {
		session.mu.Unlock()
		errorMsg := NewServerError(session.SessionID, "正在处理中，请等待完成后再发送新查询")
		h.sendMessage(session, errorMsg)
		return
	}
	session.IsProcessing = true
	session.CheckpointKey = "" // Clear previous checkpoint on new query
	session.mu.Unlock()

	// Send status message
	statusMsg := NewServerMessage(ServerStatus, session.SessionID, "开始处理您的查询...")
	h.sendMessage(session, statusMsg)

	// Create research request
	req := service.Request{
		Content:   msg.Content,
		SessionID: session.SessionID,
	}

	streamReader, err := h.ResearchService.Stream(session.Context, req)
	if err != nil {
		// This initial call should not normally interrupt, but handle it just in case
		h.handleStreamError(session, err)
		return
	}

	// Process stream and handle potential interrupts
	h.processStream(session, streamReader)
}

// handleContinue handles continue message from client
func (h *WebSocketHandler) handleContinue(session *SessionState, msg *ClientMessage) {
	session.mu.Lock()
	if !session.IsProcessing || session.CheckpointKey == "" {
		session.mu.Unlock()
		errorMsg := NewServerError(session.SessionID, "没有正在等待用户输入的处理流程")
		h.sendMessage(session, errorMsg)
		return
	}
	checkpointKey := session.CheckpointKey
	session.mu.Unlock()

	// Send status message
	statusMsg := NewServerMessage(ServerStatus, session.SessionID, "收到您的补充信息，继续分析中...")
	h.sendMessage(session, statusMsg)

	// Create research request to resume from checkpoint
	req := service.Request{
		Content:      msg.Content,
		SessionID:    session.SessionID,
		CheckpointID: checkpointKey,
	}

	streamReader, err := h.ResearchService.Resume(session.Context, req)
	if err != nil {
		h.handleStreamError(session, err)
		return
	}

	// Process stream and handle potential interrupts
	h.processStream(session, streamReader)
}

// handleCancel handles cancel message from client
func (h *WebSocketHandler) handleCancel(session *SessionState) {
	session.Cancel()

	session.mu.Lock()
	session.IsProcessing = false
	session.CheckpointKey = ""
	session.mu.Unlock()

	cancelMsg := NewServerMessage(ServerStatus, session.SessionID, "操作已取消")
	h.sendMessage(session, cancelMsg)
}

// handlePing handles ping message from client
func (h *WebSocketHandler) handlePing(session *SessionState) {
	h.connManager.UpdatePing(session.SessionID)
	pongMsg := NewServerMessage(ServerPong, session.SessionID, "")
	h.sendMessage(session, pongMsg)
}

// processStream processes the stream, sends messages to the client, and handles interrupts
func (h *WebSocketHandler) processStream(session *SessionState, streamReader *schema.StreamReader[*schema.Message]) {
	defer func() {
		session.mu.Lock()
		session.IsProcessing = false
		session.mu.Unlock()
		streamReader.Close()
	}()

	for {
		select {
		case <-session.Context.Done():
			return
		default:
			message, err := streamReader.Recv()
			if err != nil {
				h.handleStreamError(session, err)
				return
			}

			if message == nil {
				continue
			}

			// Send stream content to client
			streamMsg := NewServerMessage(ServerStream, session.SessionID, message.Content)
			h.sendMessage(session, streamMsg)
		}
	}
}

// handleStreamError centralizes error handling for Recv(), including interrupts
func (h *WebSocketHandler) handleStreamError(session *SessionState, err error) {
	// Check for graceful end of stream
	if errors.Is(err, io.EOF) {
		completeMsg := NewServerMessage(ServerComplete, session.SessionID, "分析完成")
		h.sendMessage(session, completeMsg)
		return
	}

	// --- This is the core interrupt handling logic ---
	interruptInfo, isInterrupt := compose.ExtractInterruptInfo(err)
	if isInterrupt {
		slog.Info("Interrupt detected", "session_id", session.SessionID, "interrupt_info", interruptInfo)

		session.mu.Lock()
		// For now, use session ID as checkpoint key since we need to implement proper checkpoint extraction
		session.CheckpointKey = session.SessionID
		session.mu.Unlock()

		var response *ServerMessage

		checkPoint, ok := interruptInfo.State.(state.State)
		if !ok {
			slog.Error("Failed to cast interrupt state to MemoryCheckPointStore", "session_id", session.SessionID)
			return
		}

		history := checkPoint.GetMessages()
		if len(history) > 0 && len(history[len(history)-1].ToolCalls) > 0 {
			// If the last message in the history has tool calls, it's a tool execution interrupt
			for i, toolCall := range history[len(history)-1].ToolCalls {
				toolCallInfos := fmt.Sprintf("工具调用 #%d: %s %s", i+1, toolCall.Function.Name, toolCall.Function.Arguments)
				response = NewServerMessage(ServerUserConfirmationRequired, session.SessionID, "准备执行工具操作，是否继续?\n"+toolCallInfos)
			}
		} else {
			// For now, assume this is a planner interrupt (most common case)
			// In a complete implementation, we would extract the actual node information from interruptInfo
			response = NewServerMessage(ServerCheckpoint, session.SessionID, "分析过程中需要更多信息，请提供更多详细信息以继续分析")
		}

		h.sendMessage(session, response)
		// Do not set IsProcessing to false here, as we are waiting for user input
		return
	}

	// Handle non-interrupt, non-EOF errors
	slog.Error("Stream processing error", "error", err, "session_id", session.SessionID)
	errorMsg := NewServerError(session.SessionID, fmt.Sprintf("处理失败: %v", err))
	h.sendMessage(session, errorMsg)
}

// sendMessage sends a message to the client
func (h *WebSocketHandler) sendMessage(session *SessionState, msg *ServerMessage) {
	data, err := msg.ToJSON()
	if err != nil {
		slog.Error("Failed to marshal message", slog.Any("error", err))
		return
	}

	err = session.Conn.WriteMessage(websocket.TextMessage, data)
	if err != nil {
		slog.Error("Failed to send message", slog.Any("error", err))
	}
}
