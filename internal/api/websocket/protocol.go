package websocket

import (
	"encoding/json"
	"time"
)

// MessageType defines the type of WebSocket message
type MessageType string

const (
	// Client to Server message types
	ClientQuery    MessageType = "query"    // Initial query from client
	ClientContinue MessageType = "continue" // Continue with additional input
	ClientCancel   MessageType = "cancel"   // Cancel current operation
	ClientPing     MessageType = "ping"     // Keep-alive ping

	// Server to Client message types
	ServerStatus                   MessageType = "status"                     // Status updates
	ServerStream                   MessageType = "stream"                     // Streaming content
	ServerCheckpoint               MessageType = "checkpoint"                 // Interrupt checkpoint
	ServerUserConfirmationRequired MessageType = "user_confirmation_required" // User confirmation required for tool execution
	ServerComplete                 MessageType = "complete"                   // Operation completed
	ServerError                    MessageType = "error"                      // Error occurred
	ServerPong                     MessageType = "pong"                       // Keep-alive pong
)

// BaseMessage contains common fields for all messages
type BaseMessage struct {
	Type      MessageType `json:"type"`
	SessionID string      `json:"session_id"`
	Timestamp time.Time   `json:"timestamp"`
}

// ClientMessage represents messages sent from client to server
type ClientMessage struct {
	BaseMessage
	Content string `json:"content,omitempty"` // Query content or additional input
}

// ServerMessage represents messages sent from server to client
type ServerMessage struct {
	BaseMessage
	Content string `json:"content,omitempty"` // Response content
	Error   string `json:"error,omitempty"`   // Error message if any
}

// ParseClientMessage parses a JSON message from client
func ParseClientMessage(data []byte) (*ClientMessage, error) {
	var msg ClientMessage
	err := json.Unmarshal(data, &msg)
	if err != nil {
		return nil, err
	}

	// Set timestamp if not provided
	if msg.Timestamp.IsZero() {
		msg.Timestamp = time.Now()
	}

	return &msg, nil
}

// ToJSON converts a server message to JSON bytes
func (m *ServerMessage) ToJSON() ([]byte, error) {
	// Set timestamp if not provided
	if m.Timestamp.IsZero() {
		m.Timestamp = time.Now()
	}

	return json.Marshal(m)
}

// NewServerMessage creates a new server message
func NewServerMessage(msgType MessageType, sessionID, content string) *ServerMessage {
	return &ServerMessage{
		BaseMessage: BaseMessage{
			Type:      msgType,
			SessionID: sessionID,
			Timestamp: time.Now(),
		},
		Content: content,
	}
}

// NewServerError creates a new server error message
func NewServerError(sessionID, errorMsg string) *ServerMessage {
	return &ServerMessage{
		BaseMessage: BaseMessage{
			Type:      ServerError,
			SessionID: sessionID,
			Timestamp: time.Now(),
		},
		Error: errorMsg,
	}
}

// Connection represents a WebSocket connection with session info
type Connection struct {
	SessionID string
	ConnID    string // Unique connection identifier
	Connected bool
	LastPing  time.Time
}

// ConnectionManager manages WebSocket connections
type ConnectionManager struct {
	connections map[string]*Connection // sessionID -> Connection
}

// NewConnectionManager creates a new connection manager
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		connections: make(map[string]*Connection),
	}
}

// AddConnection adds a new connection
func (cm *ConnectionManager) AddConnection(sessionID, connID string) {
	cm.connections[sessionID] = &Connection{
		SessionID: sessionID,
		ConnID:    connID,
		Connected: true,
		LastPing:  time.Now(),
	}
}

// RemoveConnection removes a connection
func (cm *ConnectionManager) RemoveConnection(sessionID string) {
	delete(cm.connections, sessionID)
}

// GetConnection gets a connection by session ID
func (cm *ConnectionManager) GetConnection(sessionID string) (*Connection, bool) {
	conn, exists := cm.connections[sessionID]
	return conn, exists
}

// UpdatePing updates the last ping time for a connection
func (cm *ConnectionManager) UpdatePing(sessionID string) {
	if conn, exists := cm.connections[sessionID]; exists {
		conn.LastPing = time.Now()
	}
}
