package service

import (
	"context"

	taloscallbacks "code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/callbacks"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/state"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/cloudwego/eino/schema"
)

// ResearchService represents the research service that interacts with the squad interface
type ResearchService struct {
	squad squad.Interface
}

// NewResearchService creates a new instance of ResearchService
func NewResearchService(squad squad.Interface) *ResearchService {
	return &ResearchService{squad: squad}
}

// Request represents the structure of a generate request
type Request struct {
	Content      string `json:"content,omitempty"`
	SessionID    string `json:"session_id,omitempty"`
	CheckpointID string `json:"checkpoint_id,omitempty"`
}

// Stream handles streaming research processing
func (s *ResearchService) Stream(ctx context.Context, req Request) (*schema.StreamReader[*schema.Message], error) {
	// Create the message
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: req.Content,
		},
	}

	// For initial stream calls, don't set checkpoint ID to avoid trying to load non-existent checkpoints
	// The checkpoint ID will be set automatically when interrupts occur and checkpoints are saved
	return s.squad.Stream(ctx, messages,
		agent.WithComposeOptions(
			compose.WithCallbacks(taloscallbacks.DefaultLogHandler.CallBackHandler()),
		),
	)
}

// Resume handles resuming research processing from a checkpoint
func (s *ResearchService) Resume(ctx context.Context, req Request) (*schema.StreamReader[*schema.Message], error) {
	// Create the message with user input for continuation
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: req.Content,
		},
	}

	return s.squad.Resume(ctx, req.CheckpointID, messages,
		agent.WithComposeOptions(
			compose.WithCallbacks(taloscallbacks.DefaultLogHandler.CallBackHandler()),
			compose.WithCheckPointID(req.SessionID),
			compose.WithStateModifier(
				func(ctx context.Context, path compose.NodePath, s any) error {
					s.(state.State).SetUserInput(req.Content)
					return nil
				},
			),
		),
	)
}
