package state

import (
	"context"
	"fmt"

	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
)

// State defines the interface for managing message state.
type State interface {
	// GetMessages returns all stored messages.
	GetMessages() []*schema.Message
	// AddMessage adds a new message to the state.
	AddMessage(message *schema.Message)
	// SetUserInput sets the user input.
	SetUserInput(input string)
	// GetUserInput returns the user input.
	GetUserInput() string
}

// AddMessageBeforeHandle updates the state with the input message and returns the input message. It is used as a pre-handler for nodes.
func AddMessageBeforeHandle(ctx context.Context, input *schema.Message, localState State) (*schema.Message, error) {
	localState.AddMessage(input)
	return input, nil
}

type state struct {
	// History stores the conversation history.
	History []*schema.Message
	// UserInput stores the user input.
	UserInput string
}

func NewState(ctx context.Context) State {
	return &state{}
}

// Ensure state implements State interface
var _ State = (*state)(nil)

func (state *state) GetMessages() []*schema.Message {
	return state.History
}

func (state *state) AddMessage(message *schema.Message) {
	state.History = append(state.History, message)
}

func (state *state) SetUserInput(input string) {
	state.UserInput = input
}

func (state *state) GetUserInput() string {
	return state.UserInput
}

// MemoryCheckPointStore is an in-MemoryCheckPointStore implementation of compose.CheckPointStore.
type MemoryCheckPointStore struct {
	checkpoints map[string][]byte
}

// NewMemoryCheckPointStore creates a new MemoryCheckPointStore.
func NewMemoryCheckPointStore() compose.CheckPointStore {
	return &MemoryCheckPointStore{
		checkpoints: make(map[string][]byte),
	}
}

// Ensure MemoryCheckPointStore implements compose.CheckPointStore
var _ compose.CheckPointStore = (*MemoryCheckPointStore)(nil)

// Get retrieves a checkpoint from the store.
func (s *MemoryCheckPointStore) Get(_ context.Context, key string) ([]byte, bool, error) {
	if data, ok := s.checkpoints[key]; ok {
		return data, ok, nil
	}
	return nil, false, fmt.Errorf("checkpoint with key %s not found", key)
}

// Put saves a checkpoint to the store.
func (s *MemoryCheckPointStore) Set(_ context.Context, key string, data []byte) error {
	s.checkpoints[key] = data
	return nil
}
