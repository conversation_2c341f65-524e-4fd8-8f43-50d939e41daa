package squad

import (
	"context"
	"fmt"
	"io"
	"strings"
	"sync"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/config"
	talosmcp "code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/doer"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/planner"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/rag/register"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/reviser"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/squad/state"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/version"
	"github.com/cloudwego/eino-ext/components/model/deepseek"
	"github.com/cloudwego/eino-ext/components/model/qwen"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/cloudwego/eino/schema"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

var (
	// Global checkpoint store to persist checkpoints across requests
	globalCheckpointStore compose.CheckPointStore
	checkpointStoreOnce   sync.Once
)

// getGlobalCheckpointStore returns the global checkpoint store instance
func getGlobalCheckpointStore() compose.CheckPointStore {
	checkpointStoreOnce.Do(func() {
		globalCheckpointStore = state.NewMemoryCheckPointStore()
	})
	return globalCheckpointStore
}

// Interface defines the methods that a squad should implement
type Interface interface {
	// Generate generates a response for the given input.
	Generate(ctx context.Context, input []*schema.Message, opts ...agent.AgentOption) (*schema.Message, error)
	// Stream generates a stream response for the given input.
	Stream(ctx context.Context, input []*schema.Message, opts ...agent.AgentOption) (*schema.StreamReader[*schema.Message], error)
	// Resume resumes a squad from a checkpoint.
	Resume(ctx context.Context, checkpointID string, input []*schema.Message, opts ...agent.AgentOption) (*schema.StreamReader[*schema.Message], error)
}

// Ensure squad implements Interface
var _ Interface = (*squad)(nil)

// squad represents the squad of agents
type squad struct {
	agent compose.Runnable[[]*schema.Message, *schema.Message]
}

// New creates a new squad instance
func New(ctx context.Context, config *config.SquadConfig) (Interface, error) {
	// Register all retrievers
	register.Init()

	deepSeek, err := newDeepSeekChatModel(ctx, config.LLMs)
	if err != nil {
		return nil, err
	}

	Qwen, err := newQwenChatModel(ctx, config.LLMs)
	if err != nil {
		return nil, err
	}

	planner := planner.New(deepSeek)
	doer := doer.New(Qwen)
	reviser := reviser.New(deepSeek)
	mcpClient, err := newMCPClient(ctx, config.MCP)
	if err != nil {
		return nil, err
	}

	agent, err := composeAgent(ctx, planner, doer, reviser, mcpClient)
	if err != nil {
		return nil, err
	}

	return &squad{agent: agent}, nil
}

// Generate implements Interface.
func (s *squad) Generate(ctx context.Context, input []*schema.Message, opts ...agent.AgentOption) (*schema.Message, error) {
	return s.agent.Invoke(ctx, input, agent.GetComposeOptions(opts...)...)
}

// Stream implements Interface.
func (s *squad) Stream(ctx context.Context, input []*schema.Message, opts ...agent.AgentOption) (*schema.StreamReader[*schema.Message], error) {
	return s.agent.Stream(ctx, input, agent.GetComposeOptions(opts...)...)
}

// Resume implements Interface.
func (s *squad) Resume(ctx context.Context, checkpointID string, input []*schema.Message, opts ...agent.AgentOption) (*schema.StreamReader[*schema.Message], error) {
	// For now, we'll use Stream with the checkpoint ID option to resume from checkpoint
	// This is a temporary implementation until we have proper Resume method in eino framework
	composeOpts := agent.GetComposeOptions(opts...)
	composeOpts = append(composeOpts, compose.WithCheckPointID(checkpointID))
	return s.agent.Stream(ctx, input, composeOpts...)
}

// newDeepSeekChatModel initializes and returns a DeepSeek chat model.
func newDeepSeekChatModel(ctx context.Context, llmsCfg *config.LLMsConfig) (model.ToolCallingChatModel, error) {
	modelConfig := &deepseek.ChatModelConfig{
		APIKey:    llmsCfg.DeepSeek.APIKey,
		BaseURL:   llmsCfg.DeepSeek.BaseURL,
		Model:     llmsCfg.DeepSeek.Model,
		MaxTokens: llmsCfg.DeepSeek.MaxTokens,
		Timeout:   llmsCfg.DeepSeek.Timeout,
	}
	return deepseek.NewChatModel(ctx, modelConfig)
}

func newQwenChatModel(ctx context.Context, llmsCfg *config.LLMsConfig) (model.ToolCallingChatModel, error) {
	modelConfig := &qwen.ChatModelConfig{
		APIKey:    llmsCfg.Qwen.APIKey,
		BaseURL:   llmsCfg.Qwen.BaseURL,
		Model:     llmsCfg.Qwen.Model,
		MaxTokens: llmsCfg.Qwen.MaxTokens,
		Timeout:   llmsCfg.Qwen.Timeout,
	}
	return qwen.NewChatModel(ctx, modelConfig)
}

// newMCPClient initializes and returns a MCP client.
func newMCPClient(ctx context.Context, cfg *config.MCPConfig) (*client.Client, error) {
	clientInfo := mcp.Implementation{
		Name:    "talos",
		Version: version.GitCommit,
	}
	return talosmcp.NewMCPClient(ctx, cfg, clientInfo)
}

const (
	// nodeKeyPlanner is the name of the planner node.
	nodeKeyPlanner = "planner"
	// nodeNamePlanner is the name of the planner node.
	nodeNamePlanner = "Oliver"

	// nodeKeyDoer is the name of the doer node.
	nodeKeyDoer = "doer"
	// nodeNameDoer is the name of the doer node.
	nodeNameDoer = "Aria"

	// nodeKeyReviser is the name of the reviser node.
	nodeKeyReviser = "reviser"
	// nodeNameReviser is the name of the reviser node.
	nodeNameReviser = "Sophia"

	// nodeKeyHuman is the name of the human node.
	nodeKeyHuman = "human"
	// nodeNameHuman is the name of the human node.
	nodeNameHuman = "Human"

	// nodeKeyTools is the name of the tools node.
	nodeKeyTools = "tools"

	// nodeKeyPlannerToList is the name of the node that sends the output of the planner to the list.
	nodeKeyPlannerToList = "planner_to_list"
	// nodeKeyDoerToList is the name of the node that sends the output of the doer to the list.
	nodeKeyDoerToList = "doer_to_list"
	// nodeKeyReviserToList is the name of the node that sends the output of the reviser to the list.
	nodeKeyReviserToList = "reviser_to_list"

	// maxStep is the maximum number of steps to take in the graph.
	maxStep = 100
)

// composeAgent composes a squad agent.
func composeAgent(
	ctx context.Context,
	planner *planner.Planner,
	doer *doer.Doer,
	reviser *reviser.Reviser,
	mcpClient *client.Client,
) (compose.Runnable[[]*schema.Message, *schema.Message], error) {
	if err := compose.RegisterSerializableType[state.State]("my state"); err != nil {
		return nil, err
	}

	toolInfos, toolsNodeConfig, err := talosmcp.GetToolsMetadata(ctx, mcpClient)
	if err != nil {
		return nil, err
	}

	doer, err = doer.WithTools(toolInfos)
	if err != nil {
		return nil, err
	}

	toolsNode, err := compose.NewToolNode(ctx, toolsNodeConfig)
	if err != nil {
		return nil, err
	}

	graph := compose.NewGraph[[]*schema.Message, *schema.Message](compose.WithGenLocalState(state.NewState))

	// Chat Model Node
	_ = graph.AddChatModelNode(nodeKeyPlanner, planner.Model, compose.WithStatePreHandler(modelPreHandler(planner.Prompt, true)), compose.WithNodeName(nodeNamePlanner))
	_ = graph.AddChatModelNode(nodeKeyDoer, doer.Model, compose.WithStatePreHandler(modelPreHandler(doer.Prompt, false)), compose.WithNodeName(nodeNameDoer))
	_ = graph.AddChatModelNode(nodeKeyReviser, reviser.Model, compose.WithStatePreHandler(modelPreHandler(reviser.Prompt, true)), compose.WithNodeName(nodeNameReviser))

	// Human Node
	_ = graph.AddLambdaNode(nodeKeyHuman, compose.InvokableLambda(humanHandle), compose.WithStatePostHandler(humanPostHandler), compose.WithNodeName(nodeNameHuman))

	// Tools Node
	_ = graph.AddToolsNode(nodeKeyTools, toolsNode, compose.WithStatePreHandler(state.AddMessageBeforeHandle), compose.WithNodeName(nodeKeyTools))

	// ToList Node
	_ = graph.AddLambdaNode(nodeKeyPlannerToList, compose.ToList[*schema.Message]())
	_ = graph.AddLambdaNode(nodeKeyDoerToList, compose.ToList[*schema.Message]())
	_ = graph.AddLambdaNode(nodeKeyReviserToList, compose.ToList[*schema.Message]())

	// Add edges to the graph
	_ = graph.AddEdge(compose.START, nodeKeyPlanner)
	_ = graph.AddEdge(nodeKeyPlanner, nodeKeyPlannerToList)
	_ = graph.AddEdge(nodeKeyPlannerToList, nodeKeyHuman)
	_ = graph.AddBranch(nodeKeyHuman, compose.NewGraphBranch(humanPostBranchCondition, map[string]bool{nodeKeyPlanner: true, nodeKeyDoer: true}))
	_ = graph.AddBranch(nodeKeyDoer, compose.NewGraphBranch(doerPostBrachCondition, map[string]bool{nodeKeyDoerToList: true, nodeKeyTools: true}))
	_ = graph.AddEdge(nodeKeyTools, nodeKeyDoer)
	_ = graph.AddEdge(nodeKeyDoerToList, nodeKeyReviser)
	_ = graph.AddBranch(nodeKeyReviser, compose.NewStreamGraphBranch(reviserPostBrachCondition, map[string]bool{nodeKeyReviserToList: true, compose.END: true}))
	_ = graph.AddEdge(nodeKeyReviserToList, nodeKeyDoer)

	runnable, err := graph.Compile(ctx,
		compose.WithNodeTriggerMode(compose.AnyPredecessor),
		compose.WithMaxRunSteps(maxStep),
		compose.WithCheckPointStore(getGlobalCheckpointStore()),
		compose.WithInterruptBeforeNodes([]string{nodeKeyHuman, nodeKeyTools}),
	)
	if err != nil {
		return nil, err
	}

	return runnable, nil
}

// modelPreHandler is a state pre-handler for the model nodes.
func modelPreHandler(systemPrompt string, isDeepSeek bool) compose.StatePreHandler[[]*schema.Message, state.State] {
	return func(ctx context.Context, input []*schema.Message, s state.State) ([]*schema.Message, error) {
		if ctx.Err() != nil {
			return nil, fmt.Errorf("context already canceled before processing: %w", ctx.Err())
		}

		for _, message := range input {
			s.AddMessage(message)
		}

		if isDeepSeek {
			return append([]*schema.Message{schema.SystemMessage(systemPrompt)}, convertMessagesForDeepSeek(s.GetMessages())...), nil
		}

		return append([]*schema.Message{schema.SystemMessage(systemPrompt)}, s.GetMessages()...), nil
	}
}

// convertMessagesForDeepSeek converts messages for DeepSeek.
func convertMessagesForDeepSeek(messages []*schema.Message) (converted []*schema.Message) {
	converted = make([]*schema.Message, 0, len(messages)*2)
	for _, message := range messages {
		switch message.Role {
		case schema.Tool:
			// tool message is not supported by DeepSeek
			converted = append(converted, schema.AssistantMessage(message.Content, nil))
		case schema.Assistant:
			if len(message.ToolCalls) == 0 {
				// assistant message without tool calls
				converted = append(converted, message)
			} else {
				// assistant message with tool calls
				if len(message.Content) > 0 {
					converted = append(converted, schema.AssistantMessage(message.Content, nil))
				}
				for _, toolCall := range message.ToolCalls {
					converted = append(converted, schema.AssistantMessage(fmt.Sprintf("call %s with %s, got response:", toolCall.Function.Name, toolCall.Function.Arguments), nil))
				}
			}
		default:
			// other roles (schema.User, schema.System) are not changed
			converted = append(converted, message)
		}
	}

	return converted
}

// humanHandle handles the human input.
func humanHandle(ctx context.Context, input []*schema.Message) (output []*schema.Message, err error) {
	return input, nil
}

// humanPostHandler handles the post-processing of the human input.
func humanPostHandler(ctx context.Context, input []*schema.Message, s state.State) ([]*schema.Message, error) {
	if ctx.Err() != nil {
		return nil, fmt.Errorf("context already canceled before processing: %w", ctx.Err())
	}

	userInput := s.GetUserInput()
	if userInput != "" {
		return []*schema.Message{schema.UserMessage(userInput)}, nil
	}
	return input, nil
}

// humanPostBranchCondition determines the next node based on the output of the human input.
func humanPostBranchCondition(_ context.Context, input []*schema.Message) (string, error) {
	if len(input) > 0 && input[len(input)-1].Role == schema.User {
		return nodeKeyPlanner, nil
	}
	return nodeKeyDoer, nil
}

// doerPostBrachCondition determines the next node based on the output of the Doer model.
func doerPostBrachCondition(_ context.Context, input *schema.Message) (string, error) {
	if len(input.ToolCalls) == 0 {
		return nodeKeyDoerToList, nil
	}

	return nodeKeyTools, nil
}

// reviserPostBrachCondition determines the next node based on the output of the Reviser model.
func reviserPostBrachCondition(_ context.Context, sr *schema.StreamReader[*schema.Message]) (string, error) {
	defer sr.Close()

	var content string
	for {
		msg, err := sr.Recv()
		if err != nil {
			if err == io.EOF {
				// If stream ends before finding the keyword, assume we need to continue the loop.
				if strings.Contains(content, "最终报告") {
					return compose.END, nil
				}
				return nodeKeyReviserToList, nil
			}
			return "", err
		}

		content += msg.Content
		if strings.Contains(content, "最终报告") {
			return compose.END, nil
		}
	}
}
