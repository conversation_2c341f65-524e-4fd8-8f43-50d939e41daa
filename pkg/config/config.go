package config

import (
	"time"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/components/model/allin"
	"github.com/cloudwego/eino-ext/components/model/deepseek"
	"github.com/cloudwego/eino-ext/components/model/ollama"
	"github.com/cloudwego/eino-ext/components/model/qwen"
)

// Config represents the main configuration structure for Talos
type Config struct {
	// GinMode specifies the Gin framework's running mode (debug/release)
	// ENV: TALOS_GINMODE
	GinMode string `mapstructure:"ginmode" json:"gin_mode"`

	// Squad contains all squad-related configurations
	// ENV: TALOS_SQUAD_*
	Squad *SquadConfig `mapstructure:"squad" json:"squad"`
}

// SquadConfig represents the configuration for the entire squad of specialists (agents) in the Talos system
type SquadConfig struct {
	// LLMs contains configurations for Large Language Models
	// ENV: TALOS_SQUAD_LLMS_*
	LLMs *LLMsConfig `mapstructure:"llms" json:"llms"`

	// MCP contains Model Control Protocol configurations
	// ENV: TALOS_SQUAD_MCP_BASEURL
	MCP *MCPConfig `mapstructure:"mcp" json:"mcp"`

	// Telemeter contains configurations for telemetry collection
	// ENV: TALOS_SQUAD_TELEMETER_*
	Telemeter *TelemeterConfig `mapstructure:"telemeter" json:"telemeter"`

	// KnowledgeSource contains configurations for knowledge base integration
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_*
	KnowledgeSource *KnowledgeSourceConfig `mapstructure:"knowledgesource" json:"knowledge_source"`
}

// LLMsConfig represents the configuration for Large Language Models
type LLMsConfig struct {
	// Ollama contains Ollama-specific configurations, only for testing
	// ENV: TALOS_SQUAD_LLMS_OLLAMA_BASEURL
	// ENV: TALOS_SQUAD_LLMS_OLLAMA_MODEL
	Ollama *ollama.ChatModelConfig `mapstructure:"ollama" json:"ollama"`
	// Allin contains Allin-specific configurations
	// ENV: TALOS_SQUAD_LLMS_ALLIN_APIKEY
	// ENV: TALOS_SQUAD_LLMS_ALLIN_BASEURL
	// ENV: TALOS_SQUAD_LLMS_ALLIN_MODEL
	// ENV: TALOS_SQUAD_LLMS_ALLIN_TEMPERATURE
	// ENV: TALOS_SQUAD_LLMS_ALLIN_MAXTOKENS
	Allin *allin.ChatModelConfig `mapstructure:"allin" json:"allin"`
	// DeepSeek contains DeepSeek-specific configurations
	// ENV: TALOS_SQUAD_LLMS_DEEPSEEK_APIKEY
	// ENV: TALOS_SQUAD_LLMS_DEEPSEEK_BASEURL
	// ENV: TALOS_SQUAD_LLMS_DEEPSEEK_MODEL
	// ENV: TALOS_SQUAD_LLMS_DEEPSEEK_TEMPERATURE
	// ENV: TALOS_SQUAD_LLMS_DEEPSEEK_MAXTOKENS
	// ENV: TALOS_SQUAD_LLMS_DEEPSEEK_TIMEOUT
	DeepSeek *deepseek.ChatModelConfig `mapstructure:"deepseek" json:"deepseek"`
	// Qwen contains Qwen-specific configurations
	// ENV: TALOS_SQUAD_LLMS_QWEN_APIKEY
	// ENV: TALOS_SQUAD_LLMS_QWEN_BASEURL
	// ENV: TALOS_SQUAD_LLMS_QWEN_MODEL
	// ENV: TALOS_SQUAD_LLMS_QWEN_TEMPERATURE
	// ENV: TALOS_SQUAD_LLMS_QWEN_MAXTOKENS
	// ENV: TALOS_SQUAD_LLMS_QWEN_TIMEOUT
	Qwen *qwen.ChatModelConfig `mapstructure:"qwen" json:"qwen"`
}

// TelemeterConfig represents the configuration for background tasks
type TelemeterConfig struct {
	// Enable controls whether background tasks are active
	// ENV: TALOS_SQUAD_TELEMETER_ENABLE
	Enable bool `mapstructure:"enable" json:"enable"`

	// SyncInterval defines how often to sync data (e.g., "15m")
	// ENV: TALOS_SQUAD_TELEMETER_SYNCINTERVAL
	SyncInterval time.Duration `mapstructure:"syncinterval" json:"sync_interval"`

	// CleanupInterval defines how often to clean up old data (e.g., "5m")
	// ENV: TALOS_SQUAD_TELEMETER_CLEANUPINTERVAL
	CleanupInterval time.Duration `mapstructure:"cleanupinterval" json:"cleanup_interval"`

	// CacheTTL defines how long to keep data in cache (e.g., "15m")
	// ENV: TALOS_SQUAD_TELEMETER_CACHETTL
	CacheTTL time.Duration `mapstructure:"cachettl" json:"cache_ttl"`

	// Source contains configurations for different data sources
	// ENV: TALOS_SQUAD_TELEMETER_SOURCE_*
	Source *SourceConfig `mapstructure:"source" json:"source"`

	// Filter contains configurations for data filtering
	// ENV: TALOS_SQUAD_TELEMETER_FILTER_*
	Filter *FilterConfig `mapstructure:"filter" json:"filter"`

	// Alerter contains configurations for alerting system
	// ENV: TALOS_SQUAD_TELEMETER_ALERTER_*
	Alerter *AlerterConfig `mapstructure:"alerter" json:"alerter"`
}

// SourceConfig represents the configuration for data sources
type SourceConfig struct {
	// EnableEvents controls whether to collect Kubernetes events
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_SOURCE_ENABLEEVENTS
	EnableEvents bool `mapstructure:"enableevents" json:"enable_events"`

	// EnableLogs controls whether to collect logs
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_SOURCE_ENABLELOGS
	EnableLogs bool `mapstructure:"enablelogs" json:"enable_logs"`

	// EnableMetrics controls whether to collect metrics
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_SOURCE_ENABLEMETRICS
	EnableMetrics bool `mapstructure:"enablemetrics" json:"enable_metrics"`

	// EnableTraces controls whether to collect traces
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_SOURCE_ENABLETRACES
	EnableTraces bool `mapstructure:"enabletraces" json:"enable_traces"`

	// MCPs contains MCP-specific configurations for each data source
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_SOURCE_MCPS_*
	MCPs map[string]*MCPConfig `mapstructure:"mcps" json:"mcps"`
}

// FilterConfig represents the configuration for data filtering
type FilterConfig struct {
	// EventTypes defines which Kubernetes event types to collect (e.g., ["Warning", "Normal"])
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_FILTER_EVENTTYPES
	EventTypes []string `mapstructure:"eventtypes" json:"event_types"`

	// NetworkFilters defines which network-related events to filter (e.g., ["NetworkNotReady", "FailedCreateEndpoint"])
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_FILTER_NETWORKFILTERS
	NetworkFilters []string `mapstructure:"networkfilters" json:"network_filters"`

	// DataSources defines source-specific filtering rules
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_FILTER_DATASOURCES_*
	DataSources SourceConfig `mapstructure:"datasources" json:"data_sources"`
}

// AlerterConfig represents the configuration for alerting
type AlerterConfig struct {
	// AlertThreshold defines how many occurrences trigger an alert
	// ENV: TALOS_SQUAD_TELEMETER_BACKGROUND_ALERTER_ALERTTHRESHOLD
	AlertThreshold int `mapstructure:"alertthreshold" json:"alert_threshold"`
}

// KnowledgeSourceConfig represents the configuration for knowledge sources
type KnowledgeSourceConfig struct {
	// Type defines the knowledge source type ("dify" or "allin")
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_TYPE
	Type string `mapstructure:"type" json:"type"`

	// Allin contains Allin-specific configurations
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_ALLIN_*
	Allin *AllinConfig `mapstructure:"allin" json:"allin"`

	// Dify contains Dify-specific configurations
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_DIFY_*
	Dify *DifyConfig `mapstructure:"dify" json:"dify"`
}

// AllinConfig represents Allin-specific configuration
type AllinConfig struct {
	// Endpoint defines the base URL for Allin API requests
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_ALLIN_ENDPOINT
	Endpoint string `mapstructure:"endpoint" json:"endpoint"`

	// APPKey defines the authentication key for Allin API requests
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_ALLIN_APPKEY
	APPKey string `mapstructure:"appkey" json:"appkey"`

	// APPID defines the ID of the knowledge base to use
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_ALLIN_APPID
	APPID string `mapstructure:"appid" json:"appid"`
}

// DifyConfig represents Dify-specific configuration
type DifyConfig struct {
	// DatasetAPIKey defines the API key for Dify dataset access
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_DIFY_DATASETAPIKEY
	DatasetAPIKey string `mapstructure:"datasetapikey" json:"dataset_api_key"`

	// Endpoint defines the Dify API endpoint URL
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_DIFY_ENDPOINT
	Endpoint string `mapstructure:"endpoint" json:"endpoint"`

	// DatasetID defines the ID of the Dify dataset to use
	// ENV: TALOS_SQUAD_KNOWLEDGESOURCE_DIFY_DATASETID
	DatasetID string `mapstructure:"datasetid" json:"dataset_id"`
}

// MCPConfig represents the configuration for MCP services
type MCPConfig struct {
	// BaseUrl defines the base URL for MCP service
	// ENV: TALOS_SQUAD_MCP_BASEURL
	BaseUrl string `mapstructure:"baseurl" json:"base_url"`
}
