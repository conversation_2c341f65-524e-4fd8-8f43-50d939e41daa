package kubernetes

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"strings"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"
	metrics "k8s.io/metrics/pkg/client/clientset/versioned"
)

// Interface defines the behavior of a Kubernetes client.
type Interface interface {
	// CreateResource creates a new Kubernetes resource in the specified namespace
	CreateResource(ctx context.Context, kind, namespace, manifest string) ([]byte, error)
	// DeleteResource removes a Kubernetes resource from the specified namespace
	DeleteResource(ctx context.Context, kind, namespace, name string) error
	// UpdateResource modifies an existing Kubernetes resource in the specified namespace
	UpdateResource(ctx context.Context, kind, namespace, manifest string) ([]byte, error)
	// GetResource retrieves a specific Kubernetes resource from the specified namespace
	GetResource(ctx context.Context, kind, namespace, name string) ([]byte, error)
	// ListResources retrieves a list of Kubernetes resources based on the provided filters
	ListResources(ctx context.Context, kind, namespace, labelSelector, fieldSelector string) ([]byte, error)
	// GetClusterInfo retrieves general information about the Kubernetes cluster
	GetClusterInfo(ctx context.Context) ([]byte, error)
	// GetPodLogs retrieves logs from a specific pod in the specified namespace
	GetPodLogs(ctx context.Context, namespace, name, container string, tailLines, sinceSeconds int64) ([]byte, error)
	// ExecuteCommand executes a command in a specific container of a pod
	ExecuteCommand(ctx context.Context, namespace, podName, containerName string, command []string) ([]byte, error)
	// GetResourceUsage retrieves resource usage (CPU and Memory) for pods or nodes
	GetResourceUsage(ctx context.Context, resourceType, namespace, name string) ([]byte, error)
	// Close terminates the client connection and cleans up resources
	Close()
}

// client represents the internal implementation of the Kubernetes client.
type client struct {
	// Standard Kubernetes clientset for core operations
	clientSet kubernetes.Interface
	// Dynamic client for working with custom resources
	dynamicClient dynamic.Interface
	// Discovery client for API resource information
	discoveryClient discovery.DiscoveryInterface
	// Metrics client for fetching resource usage data
	metricsClient *metrics.Clientset
	// REST configuration for the Kubernetes cluster
	restConfig *rest.Config
	// Cache of known API resources
	resourceMap map[string]*schema.GroupVersionResource
	// Mutex for safe concurrent access to resourceMap
	resourceMapMux sync.RWMutex
	// Timestamp of the last resource map refresh
	lastRefreshTime time.Time
	// Interval for periodic resource map refreshes
	refreshInterval time.Duration
	// Channel to signal stopping of background processes
	stopCh chan struct{}
}

// Ensure that client implements Interface
var _ Interface = &client{}

// ClientOption defines configuration options for the client
type ClientOption func(*client)

// WithRefreshInterval sets the refresh interval for the resource map cache
func WithRefreshInterval(d time.Duration) ClientOption {
	return func(c *client) {
		c.refreshInterval = d
	}
}

// New creates a new Kubernetes client with the provided kubeconfig path and optional configuration options.
func New(kubeConfigPath string, opts ...ClientOption) (Interface, error) {
	var (
		restConfig *rest.Config
		err        error
	)

	// Initialize REST config based on kubeconfig path or in-cluster config
	if kubeConfigPath != "" {
		restConfig, err = clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	} else {
		restConfig, err = rest.InClusterConfig()
	}
	if err != nil {
		return nil, err
	}
	if restConfig == nil {
		return nil, fmt.Errorf("failed to get restConfig")
	}

	// Create a new client instance with default configuration
	c := &client{
		restConfig:      restConfig,
		clientSet:       kubernetes.NewForConfigOrDie(restConfig),
		dynamicClient:   dynamic.NewForConfigOrDie(restConfig),
		discoveryClient: discovery.NewDiscoveryClientForConfigOrDie(restConfig),
		metricsClient:   metrics.NewForConfigOrDie(restConfig),
		resourceMap:     make(map[string]*schema.GroupVersionResource),
		refreshInterval: 60 * time.Minute, // Default 60 minutes refresh interval
		stopCh:          make(chan struct{}),
	}

	// Apply any provided configuration options
	for _, opt := range opts {
		opt(c)
	}

	// Initialize the resource map
	if err := c.refreshResourceMap(); err != nil {
		return nil, err
	}

	// Start the background refresh loop
	go c.startRefreshLoop()

	return c, nil
}

// refreshResourceMap updates the client's resource map with the latest API resources from the server.
// It fetches the preferred resources, parses them, and creates a new map of GroupVersionResources.
// This function is crucial for maintaining an up-to-date cache of available API resources.
func (c *client) refreshResourceMap() error {
	// Fetch the server's preferred resources
	apiResourceList, err := c.discoveryClient.ServerPreferredResources()
	if err != nil {
		// Handle partial errors in API group discovery
		if !discovery.IsGroupDiscoveryFailedError(err) {
			return fmt.Errorf("failed to get server resources: %w", err)
		}

		slog.LogAttrs(context.Background(), slog.LevelWarn,
			"Some API groups discovery failed", slog.Any("error", err))
	}

	// Initialize new maps for the update
	newResourceMap := make(map[string]*schema.GroupVersionResource)
	seenKinds := make(map[string]bool)

	// Iterate through all API groups
	for _, group := range apiResourceList {
		if group == nil {
			continue
		}

		// Parse the group version
		gv, err := schema.ParseGroupVersion(group.GroupVersion)
		if err != nil {
			slog.LogAttrs(context.Background(), slog.LevelWarn,
				"Failed to parse group version",
				slog.String("groupVersion", group.GroupVersion),
				slog.Any("error", err))
			continue
		}

		// Process each API resource in the group
		for _, apiResource := range group.APIResources {
			// Skip subresources
			if strings.Contains(apiResource.Name, "/") {
				continue
			}

			// Ensure the resource supports required verbs
			if !hasRequiredVerbs(apiResource.Verbs, []string{"get", "list"}) {
				continue
			}

			// Add the resource to the map if it's not already seen
			if _, exists := seenKinds[apiResource.Kind]; !exists {
				seenKinds[apiResource.Kind] = true
				newResourceMap[apiResource.Kind] = &schema.GroupVersionResource{
					Group:    gv.Group,
					Version:  gv.Version,
					Resource: apiResource.Name,
				}
			}
		}
	}

	// Update the client's resource map and refresh time
	c.resourceMapMux.Lock()
	c.resourceMap = newResourceMap
	c.lastRefreshTime = time.Now()
	c.resourceMapMux.Unlock()

	return nil
}

// hasRequiredVerbs checks if a list of available verbs contains all required verbs.
func hasRequiredVerbs(available []string, required []string) bool {
	availableSet := make(map[string]bool)
	for _, verb := range available {
		availableSet[verb] = true
	}

	for _, verb := range required {
		if !availableSet[verb] {
			return false
		}
	}

	return true
}

// startRefreshLoop starts a background goroutine that periodically refreshes the resource map.
// It uses a ticker to trigger refreshes at the specified interval and can be stopped by closing the stopCh.
func (c *client) startRefreshLoop() {
	ticker := time.NewTicker(c.refreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Attempt to refresh the resource map
			if err := c.refreshResourceMap(); err != nil {
				slog.LogAttrs(context.Background(), slog.LevelWarn,
					"Failed to refresh resource map", slog.Any("error", err))
			}
		case <-c.stopCh:
			// Stop the refresh loop when the stop channel is closed
			return
		}
	}
}

// findGroupVersionResource retrieves the GroupVersionResource for a given kind.
// It first checks the cached resourceMap, and if not found or outdated, refreshes the map.
// Parameters:
// - kind: The kind of resource to find (e.g., "Pod", "Deployment")
//
// Returns a pointer to schema.GroupVersionResource and an error if not found.
func (c *client) findGroupVersionResource(kind string) (*schema.GroupVersionResource, error) {
	// Validate input
	if kind == "" {
		return nil, fmt.Errorf("kind cannot be empty")
	}

	// Check cache with read lock
	c.resourceMapMux.RLock()
	gvr, exists := c.resourceMap[kind]
	lastRefresh := c.lastRefreshTime
	c.resourceMapMux.RUnlock()

	// Return cached result if valid
	if exists && time.Since(lastRefresh) < c.refreshInterval {
		return gvr, nil
	}

	// Refresh resource map if cache is outdated or missing
	if err := c.refreshResourceMap(); err != nil {
		return nil, fmt.Errorf("failed to refresh resource map: %w", err)
	}

	// Check updated cache
	c.resourceMapMux.RLock()
	defer c.resourceMapMux.RUnlock()
	if gvr, exists := c.resourceMap[kind]; exists {
		return gvr, nil
	}

	// Return error if resource not found
	return nil, fmt.Errorf("resource with kind %q not found in the cluster", kind)
}

// CreateResource creates a new Kubernetes resource from a JSON manifest.
// Parameters:
// - ctx: Context for the operation
// - kind: The kind of resource to create (e.g., "Pod", "Deployment")
// - namespace: The namespace to create the resource in (empty for cluster-scoped resources)
// - manifest: JSON string containing the resource definition
//
// Returns the created resource as a map[string]any or an error if the operation fails.
func (c *client) CreateResource(ctx context.Context, kind string, namespace string, manifest string) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	// Validate parameters
	if kind == "" {
		return nil, fmt.Errorf("kind cannot be empty")
	}

	if manifest == "" {
		return nil, fmt.Errorf("manifest cannot be empty")
	}

	// Reuse existing GVR cache mechanism
	gvr, err := c.findGroupVersionResource(kind)
	if err != nil {
		return nil, fmt.Errorf("failed to find resource type %q: %s", kind, err)
	}

	// Parse resource manifest
	obj := &unstructured.Unstructured{}
	if err := obj.UnmarshalJSON([]byte(manifest)); err != nil {
		return nil, fmt.Errorf("failed to unmarshal manifest: %s", err)
	}

	// Call different creation methods based on whether a namespace is provided
	var result *unstructured.Unstructured
	if namespace != "" {
		result, err = c.dynamicClient.Resource(*gvr).Namespace(namespace).Create(ctx, obj, metav1.CreateOptions{})
	} else {
		result, err = c.dynamicClient.Resource(*gvr).Create(ctx, obj, metav1.CreateOptions{})
	}
	if err != nil {
		return nil, fmt.Errorf("failed to create resource (kind: %s) in namespace %q: %s", kind, namespace, err)
	}

	return result.MarshalJSON()
}

// DeleteResource deletes a Kubernetes resource by kind, namespace, and name.
// Parameters:
// - ctx: Context for the operation
// - kind: The kind of resource to delete (e.g., "Pod", "Deployment")
// - namespace: The namespace of the resource (empty for cluster-scoped resources)
// - name: The name of the resource to delete
//
// Returns an error if the operation fails.
func (c *client) DeleteResource(ctx context.Context, kind string, namespace string, name string) error {
	// Check context status
	if err := ctx.Err(); err != nil {
		return fmt.Errorf("context error: %s", err)
	}

	// Validate parameters
	if kind == "" {
		return fmt.Errorf("kind cannot be empty")
	}

	if name == "" {
		return fmt.Errorf("resource name cannot be empty")
	}

	// Reuse existing GVR cache mechanism
	gvr, err := c.findGroupVersionResource(kind)
	if err != nil {
		return fmt.Errorf("failed to find resource type %q: %s", kind, err)
	}

	// Call different deletion methods based on whether a namespace is provided
	if namespace != "" {
		err = c.dynamicClient.Resource(*gvr).Namespace(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	} else {
		err = c.dynamicClient.Resource(*gvr).Delete(ctx, name, metav1.DeleteOptions{})
	}
	if err != nil {
		return fmt.Errorf("failed to delete resource %q (kind: %s) in namespace %q: %s", name, kind, namespace, err)
	}

	return nil
}

// UpdateResource updates an existing Kubernetes resource from a JSON manifest.
// Parameters:
// - ctx: Context for the operation
// - kind: The kind of resource to update (e.g., "Pod", "Deployment")
// - namespace: The namespace of the resource (empty for cluster-scoped resources)
// - manifest: JSON string containing the updated resource definition
//
// Returns the updated resource as a map[string]any or an error if the operation fails.
// The manifest must include the resource name in its metadata.
func (c *client) UpdateResource(ctx context.Context, kind string, namespace string, manifest string) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	// Validate parameters
	if kind == "" {
		return nil, fmt.Errorf("kind cannot be empty")
	}

	if manifest == "" {
		return nil, fmt.Errorf("manifest cannot be empty")
	}

	// Reuse existing GVR cache mechanism
	gvr, err := c.findGroupVersionResource(kind)
	if err != nil {
		return nil, fmt.Errorf("failed to find resource type %q: %s", kind, err)
	}

	// Parse resource manifest
	obj := &unstructured.Unstructured{}
	if err := obj.UnmarshalJSON([]byte(manifest)); err != nil {
		return nil, fmt.Errorf("failed to unmarshal manifest: %s", err)
	}

	// Check resource name
	name, found, err := unstructured.NestedString(obj.Object, "metadata", "name")
	if err != nil {
		return nil, fmt.Errorf("failed to get resource name from manifest: %s", err)
	}
	if !found || name == "" {
		return nil, fmt.Errorf("resource name not found in manifest")
	}

	// Call different update methods based on whether a namespace is provided
	var result *unstructured.Unstructured
	if namespace != "" {
		result, err = c.dynamicClient.Resource(*gvr).Namespace(namespace).Update(ctx, obj, metav1.UpdateOptions{})
	} else {
		result, err = c.dynamicClient.Resource(*gvr).Update(ctx, obj, metav1.UpdateOptions{})
	}
	if err != nil {
		return nil, fmt.Errorf("failed to update resource %q (kind: %s) in namespace %q: %s", name, kind, namespace, err)
	}

	return result.MarshalJSON()
}

// GetResource retrieves a Kubernetes resource by kind, namespace, and name.
// Parameters:
// - ctx: Context for the operation
// - kind: The kind of resource to retrieve (e.g., "Pod", "Deployment")
// - namespace: The namespace of the resource (empty for cluster-scoped resources)
// - name: The name of the resource to retrieve
//
// Returns the resource as a map[string]any or an error if the operation fails.
func (c *client) GetResource(ctx context.Context, kind string, namespace string, name string) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	// Validate parameters
	if kind == "" {
		return nil, fmt.Errorf("kind cannot be empty")
	}

	if name == "" {
		return nil, fmt.Errorf("resource name cannot be empty")
	}

	// Reuse existing GVR cache mechanism
	gvr, err := c.findGroupVersionResource(kind)
	if err != nil {
		return nil, fmt.Errorf("failed to find resource type %q: %s", kind, err)
	}

	var result *unstructured.Unstructured
	if namespace != "" {
		result, err = c.dynamicClient.Resource(*gvr).Namespace(namespace).Get(ctx, name, metav1.GetOptions{})
	} else {
		result, err = c.dynamicClient.Resource(*gvr).Get(ctx, name, metav1.GetOptions{})
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get resource %q (kind: %s) in namespace %q: %s", name, kind, namespace, err)
	}

	return result.MarshalJSON()
}

// ListResources retrieves a list of Kubernetes resources by kind, with optional filtering.
// Parameters:
// - ctx: Context for the operation
// - kind: The kind of resources to list (e.g., "Pod", "Deployment")
// - namespace: The namespace to list resources from (empty for cluster-scoped resources or to list across all namespaces)
// - labelSelector: Optional label selector to filter resources (e.g., "app=nginx")
// - fieldSelector: Optional field selector to filter resources (e.g., "status.phase=Running")
//
// Returns a slice of resources as []map[string]any or an error if the operation fails.
// Note: For large clusters, this method may put significant load on the API server.
// Consider implementing pagination for production use.
func (c *client) ListResources(ctx context.Context, kind string, namespace string, labelSelector string, fieldSelector string) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	// Validate parameters
	if kind == "" {
		return nil, fmt.Errorf("kind cannot be empty")
	}

	// Reuse existing GVR cache mechanism
	gvr, err := c.findGroupVersionResource(kind)
	if err != nil {
		return nil, fmt.Errorf("failed to find resource type %q: %s", kind, err)
	}

	// Build list options
	listOptions := metav1.ListOptions{}
	if labelSelector != "" {
		listOptions.LabelSelector = labelSelector
	}
	if fieldSelector != "" {
		listOptions.FieldSelector = fieldSelector
	}

	// Call different result methods based on whether a namespace is provided
	var result *unstructured.UnstructuredList
	if namespace != "" {
		result, err = c.dynamicClient.Resource(*gvr).Namespace(namespace).List(ctx, listOptions)
	} else {
		result, err = c.dynamicClient.Resource(*gvr).List(ctx, listOptions)
	}
	if err != nil {
		return nil, fmt.Errorf("failed to list resources (kind: %s) in namespace %q: %s", kind, namespace, err)
	}

	return result.MarshalJSON()
}

// GetClusterInfo retrieves cluster metadata and status information.
// This method collects various information about the Kubernetes cluster including:
// - Cluster version
// - API groups and resources
// - Component status
// - Node information
// - Namespace information
// - Resource statistics (pods, services, deployments, etc.)
//
// Note: This method may put significant load on the API server in large clusters.
// Consider implementing caching or pagination for production use.
func (c *client) GetClusterInfo(ctx context.Context) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	// Get Kubernetes server version information
	version, err := c.discoveryClient.ServerVersion()
	if err != nil {
		return nil, fmt.Errorf("failed to get server version: %s", err)
	}

	// Get available API groups and resources from the Kubernetes cluster
	apiGroups, _, err := c.discoveryClient.ServerGroupsAndResources()
	if err != nil {
		if !discovery.IsGroupDiscoveryFailedError(err) {
			return nil, fmt.Errorf("failed to get API resources: %s", err)
		}
		// Partial API group discovery failure is normal, log a warning but continue
		slog.LogAttrs(context.Background(), slog.LevelWarn,
			"Some API groups discovery failed", slog.Any("error", err))
	}

	// Get status of cluster components (e.g., scheduler, controller-manager)
	componentStatus, err := c.clientSet.CoreV1().ComponentStatuses().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get component status: %s", err)
	}

	// Get node information
	nodes, err := c.clientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get nodes: %s", err)
	}

	// Get namespace information
	namespaces, err := c.clientSet.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get namespaces: %s", err)
	}

	// Build a formatted string containing cluster information
	result := map[string]any{
		"version":         version,
		"apiGroups":       apiGroups,
		"componentStatus": componentStatus,
		"nodes":           nodes,
		"namespaces":      namespaces,
	}

	return json.Marshal(result)
}

// GetPodLogs retrieves logs from a specific pod in the specified namespace.
// Parameters:
// - ctx: Context for the operation
// - namespace: The namespace of the pod
// - name: The name of the pod
// - container: The name of the container in the pod (optional)
// - tailLines: The number of lines to tail from the end of the logs (optional)
// - sinceSeconds: Return logs newer than this many seconds (optional)
//
// Returns the pod logs as a byte array or an error if the operation fails.
func (c *client) GetPodLogs(ctx context.Context, namespace, name, container string, tailLines, sinceSeconds int64) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	// Validate parameters
	if namespace == "" {
		return nil, fmt.Errorf("namespace cannot be empty")
	}
	if name == "" {
		return nil, fmt.Errorf("pod name cannot be empty")
	}

	// Create log options
	logOptions := &corev1.PodLogOptions{
		Follow: false,
	}
	if container != "" {
		logOptions.Container = container
	}
	if tailLines > 0 {
		logOptions.TailLines = &tailLines
	}
	if sinceSeconds > 0 {
		logOptions.SinceSeconds = &sinceSeconds
	}

	// Get logs
	req := c.clientSet.CoreV1().Pods(namespace).GetLogs(name, logOptions)
	podLogs, err := req.Stream(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get logs for pod %q in namespace %q: %s", name, namespace, err)
	}
	defer podLogs.Close()

	buf := new(strings.Builder)
	_, err = io.Copy(buf, podLogs)
	if err != nil {
		return nil, fmt.Errorf("failed to read logs for pod %q in namespace %q: %s", name, namespace, err)
	}

	return []byte(buf.String()), nil
}

// ExecuteCommand executes a command in a specific container of a pod.
// Parameters:
// - ctx: Context for the operation
// - namespace: The namespace of the pod
// - podName: The name of the pod
// - containerName: The name of the container in the pod
// - command: The command and its arguments to execute
//
// Returns the command output as a byte array or an error if the operation fails.
func (c *client) ExecuteCommand(ctx context.Context, namespace, podName, containerName string, command []string) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	// Validate parameters
	if namespace == "" {
		return nil, fmt.Errorf("namespace cannot be empty")
	}
	if podName == "" {
		return nil, fmt.Errorf("pod name cannot be empty")
	}
	if containerName == "" {
		return nil, fmt.Errorf("container name cannot be empty")
	}
	if len(command) == 0 {
		return nil, fmt.Errorf("command cannot be empty")
	}

	// Create a POST request for the exec command
	req := c.clientSet.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Container: containerName,
			Command:   command,
			Stdin:     false,
			Stdout:    true,
			Stderr:    true,
			TTY:       false,
		}, metav1.ParameterCodec)

	exec, err := remotecommand.NewSPDYExecutor(c.restConfig, "POST", req.URL())
	if err != nil {
		return nil, fmt.Errorf("failed to create SPDY executor: %w", err)
	}

	var stdout, stderr bytes.Buffer
	err = exec.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to execute command: %w, stderr: %s", err, stderr.String())
	}

	return stdout.Bytes(), nil
}

// GetResourceUsage retrieves resource usage (CPU and Memory) for pods or nodes.
// Parameters:
// - ctx: Context for the operation
// - resourceType: The type of resource to get usage for ("pod" or "node")
// - namespace: The namespace of the pod (required if resourceType is "pod", ignored for "node")
// - name: The name of the specific pod or node (optional, if empty, lists all)
//
// Returns the resource usage data as a byte array or an error if the operation fails.
func (c *client) GetResourceUsage(ctx context.Context, resourceType, namespace, name string) ([]byte, error) {
	// Check context status
	if err := ctx.Err(); err != nil {
		return nil, fmt.Errorf("context error: %s", err)
	}

	var result any
	var err error

	switch resourceType {
	case "pod":
		if name != "" {
			// Get usage for a specific pod
			result, err = c.metricsClient.MetricsV1beta1().PodMetricses(namespace).Get(ctx, name, metav1.GetOptions{})
		} else {
			// List usage for all pods in a namespace or all namespaces
			result, err = c.metricsClient.MetricsV1beta1().PodMetricses(namespace).List(ctx, metav1.ListOptions{})
		}
	case "node":
		if name != "" {
			// Get usage for a specific node
			result, err = c.metricsClient.MetricsV1beta1().NodeMetricses().Get(ctx, name, metav1.GetOptions{})
		} else {
			// List usage for all nodes
			result, err = c.metricsClient.MetricsV1beta1().NodeMetricses().List(ctx, metav1.ListOptions{})
		}
	default:
		return nil, fmt.Errorf("unsupported resource type: %s. Must be 'pod' or 'node'", resourceType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get resource usage for %s %s: %w", resourceType, name, err)
	}

	return json.Marshal(result)
}

// Close shuts down the client and stops the background refresh goroutine
func (c *client) Close() {
	close(c.stopCh)
}
