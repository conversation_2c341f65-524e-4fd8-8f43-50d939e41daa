package mcp

import (
	"context"
	"fmt"
	"log/slog"

	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/auth"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp/kubernetes"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp/prompt"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/mcp/tools"
	"code.devops.xiaohongshu.com/cloud-native/talos/pkg/version"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// Interface defines the methods that an MCP server should implement
type Interface interface {
	// ServeStdio starts the MCP server in stdio mode
	ServeStdio() error
	// ServeSSE starts the MCP server in Server-Sent Events (SSE) mode
	ServeSSE() *server.SSEServer
}

// Ensure mcpServer implements Interface
var _ Interface = &mcpServer{}

// mcpServer is the concrete implementation of the Interface
type mcpServer struct {
	*server.MCPServer // Embedding the MCPServer from the mcp-go package
}

// NewServer creates and initializes a new Kubernetes MCP (Management Control Protocol) server
func NewServer(readOnly bool, clusterName, kubeConfigPath string) (Interface, error) {
	// Create Kubernetes client
	client, err := kubernetes.New(kubeConfigPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kubernetes client: %w", err)
	}

	// Initialize and get server tools
	tool := tools.New(readOnly, client)
	serverTools, err := tool.GetServerTools()
	if err != nil {
		return nil, fmt.Errorf("failed to get server tools: %w", err)
	}

	// Create new MCP server with configurations
	srv := server.NewMCPServer(
		"talos-kubernetes-server",
		version.GitCommit,
		server.WithPromptCapabilities(true),
		server.WithToolCapabilities(true),
		server.WithLogging(),
		server.WithHooks(newHooks()),
	)

	// Add default prompt and handler
	srv.AddPrompt(prompt.DefaultPrompt, prompt.DefaultPromptHandlerFunc(clusterName))

	// Add server tools
	srv.AddTools(serverTools...)

	// Return initialized MCP server
	return &mcpServer{srv}, nil
}

// ServeStdio starts the MCP server in stdio mode
func (s *mcpServer) ServeStdio() error {
	return server.ServeStdio(
		s.MCPServer,
		server.WithStdioContextFunc(auth.AuthFromEnv),
	)
}

// ServeSSE starts the MCP server in Server-Sent Events (SSE) mode
func (s *mcpServer) ServeSSE() *server.SSEServer {
	return server.NewSSEServer(s.MCPServer)
}

// newHooks creates and configures server hooks for various MCP operations
func newHooks() *server.Hooks {
	hooks := &server.Hooks{}

	// Hook: Pre-processing for any MCP request
	hooks.AddBeforeAny(func(ctx context.Context, id any, method mcp.MCPMethod, message any) {
		slog.LogAttrs(ctx, slog.LevelDebug, "MCP request received",
			slog.Any("request_id", id),
			slog.String("method", string(method)),
			slog.String("message_type", fmt.Sprintf("%T", message)),
		)
	})

	// Hook: Post-processing for successful MCP requests
	hooks.AddOnSuccess(func(ctx context.Context, id any, method mcp.MCPMethod, message any, result any) {
		slog.LogAttrs(ctx, slog.LevelDebug, "MCP request succeeded",
			slog.Any("request_id", id),
			slog.String("method", string(method)),
			slog.String("result_type", fmt.Sprintf("%T", result)),
		)
	})

	// Hook: Error handling for failed MCP requests
	hooks.AddOnError(func(ctx context.Context, id any, method mcp.MCPMethod, message any, err error) {
		slog.LogAttrs(ctx, slog.LevelError, "MCP request failed",
			slog.Any("request_id", id),
			slog.String("method", string(method)),
			slog.Any("error", err),
			slog.String("message_type", fmt.Sprintf("%T", message)),
		)
	})

	// Hook: Pre-initialization of MCP server
	hooks.AddBeforeInitialize(func(ctx context.Context, id any, message *mcp.InitializeRequest) {
		slog.LogAttrs(ctx, slog.LevelInfo, "MCP server initializing",
			slog.Any("request_id", id),
			slog.String("protocol_version", message.Params.ProtocolVersion),
			slog.Any("client_info", message.Params.ClientInfo),
		)
	})

	// Hook: Post-initialization of MCP server
	hooks.AddAfterInitialize(func(ctx context.Context, id any, message *mcp.InitializeRequest, result *mcp.InitializeResult) {
		slog.LogAttrs(ctx, slog.LevelInfo, "MCP server initialized",
			slog.Any("request_id", id),
			slog.String("protocol_version", result.ProtocolVersion),
			slog.Any("server_info", result.ServerInfo),
		)
	})

	// Hook: Pre-execution of tool calls
	hooks.AddBeforeCallTool(func(ctx context.Context, id any, message *mcp.CallToolRequest) {
		slog.LogAttrs(ctx, slog.LevelDebug, "Tool execution starting",
			slog.Any("request_id", id),
			slog.String("tool_name", message.Params.Name),
			slog.Any("arguments", message.Params.Arguments),
		)
	})

	// Hook: Post-execution of tool calls
	hooks.AddAfterCallTool(func(ctx context.Context, id any, message *mcp.CallToolRequest, result *mcp.CallToolResult) {
		slog.LogAttrs(ctx, slog.LevelDebug, "Tool execution completed",
			slog.Any("request_id", id),
			slog.String("tool_name", message.Params.Name),
			slog.Bool("is_error", result.IsError),
			slog.Int("content_length", len(result.Content)),
		)
	})

	return hooks
}
